/* 
 * StudentsCMSSP 企业级按钮系统
 * 与主题系统集成，确保稳定、快速、整洁的企业级体验
 * 版本: 2.0.0 - 主题集成版
 */

/* ==================== 企业级按钮基础样式 ==================== */
.btn {
    font-size: 13px;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 4px;
    border-width: 1px;
    border-style: solid;
    transition: all 0.15s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    cursor: pointer;
    line-height: 1.4;
    white-space: nowrap;
    user-select: none;
    vertical-align: middle;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(var(--theme-primary-rgb), 0.25);
}

.btn:disabled,
.btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* ==================== 按钮尺寸 ==================== */
.btn-xs {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 3px;
}

.btn-sm {
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 3px;
}

.btn-lg {
    font-size: 14px;
    padding: 12px 24px;
    border-radius: 5px;
}

.btn-xl {
    font-size: 16px;
    padding: 16px 32px;
    border-radius: 6px;
}

/* ==================== 主要按钮样式 ==================== */
.btn-primary {
    background-color: var(--theme-primary);
    border-color: var(--theme-primary);
    color: white;
}

.btn-primary:hover,
.btn-primary:focus {
    background-color: var(--theme-primary-dark);
    border-color: var(--theme-primary-dark);
    color: white;
}

.btn-primary:active {
    background-color: var(--theme-primary-dark);
    border-color: var(--theme-primary-dark);
    color: white;
    transform: translateY(1px);
}

/* ==================== 次要按钮样式 ==================== */
.btn-secondary {
    background-color: var(--theme-secondary);
    border-color: var(--theme-secondary);
    color: white;
}

.btn-secondary:hover,
.btn-secondary:focus {
    background-color: var(--theme-secondary-dark);
    border-color: var(--theme-secondary-dark);
    color: white;
}

/* ==================== 轮廓按钮样式 ==================== */
.btn-outline-primary {
    background-color: transparent;
    border-color: var(--theme-primary);
    color: var(--theme-primary);
}

.btn-outline-primary:hover,
.btn-outline-primary:focus {
    background-color: var(--theme-primary);
    border-color: var(--theme-primary);
    color: white;
}

.btn-outline-secondary {
    background-color: transparent;
    border-color: var(--theme-secondary);
    color: var(--theme-secondary);
}

.btn-outline-secondary:hover,
.btn-outline-secondary:focus {
    background-color: var(--theme-secondary);
    border-color: var(--theme-secondary);
    color: white;
}

.btn-outline-success {
    background-color: transparent;
    border-color: var(--theme-success);
    color: var(--theme-success);
}

.btn-outline-success:hover,
.btn-outline-success:focus {
    background-color: var(--theme-success);
    border-color: var(--theme-success);
    color: white;
}

.btn-outline-warning {
    background-color: transparent;
    border-color: var(--theme-warning);
    color: var(--theme-warning);
}

.btn-outline-warning:hover,
.btn-outline-warning:focus {
    background-color: var(--theme-warning);
    border-color: var(--theme-warning);
    color: var(--theme-gray-800, #343a40);
}

.btn-outline-danger {
    background-color: transparent;
    border-color: var(--theme-danger);
    color: var(--theme-danger);
}

.btn-outline-danger:hover,
.btn-outline-danger:focus {
    background-color: var(--theme-danger);
    border-color: var(--theme-danger);
    color: white;
}

.btn-outline-info {
    background-color: transparent;
    border-color: var(--theme-info);
    color: var(--theme-info);
}

.btn-outline-info:hover,
.btn-outline-info:focus {
    background-color: var(--theme-info);
    border-color: var(--theme-info);
    color: white;
}

/* ==================== 语义化按钮样式 ==================== */
.btn-success {
    background-color: var(--theme-success);
    border-color: var(--theme-success);
    color: white;
}

.btn-success:hover,
.btn-success:focus {
    background-color: var(--theme-success-dark);
    border-color: var(--theme-success-dark);
    color: white;
}

.btn-warning {
    background-color: var(--theme-warning);
    border-color: var(--theme-warning);
    color: var(--theme-gray-800, #343a40);
}

.btn-warning:hover,
.btn-warning:focus {
    background-color: var(--theme-warning-dark);
    border-color: var(--theme-warning-dark);
    color: var(--theme-gray-800, #343a40);
}

.btn-danger {
    background-color: var(--theme-danger);
    border-color: var(--theme-danger);
    color: white;
}

.btn-danger:hover,
.btn-danger:focus {
    background-color: var(--theme-danger-dark);
    border-color: var(--theme-danger-dark);
    color: white;
}

.btn-info {
    background-color: var(--theme-info);
    border-color: var(--theme-info);
    color: white;
}

.btn-info:hover,
.btn-info:focus {
    background-color: var(--theme-info-dark);
    border-color: var(--theme-info-dark);
    color: white;
}

/* ==================== 特殊按钮样式 ==================== */
.btn-light {
    background-color: var(--theme-gray-100, #f8f9fa);
    border-color: var(--theme-gray-300, #dee2e6);
    color: var(--theme-gray-800, #343a40);
}

.btn-light:hover,
.btn-light:focus {
    background-color: var(--theme-gray-200, #e9ecef);
    border-color: var(--theme-gray-400, #ced4da);
    color: var(--theme-gray-800, #343a40);
}

.btn-dark {
    background-color: var(--theme-gray-800, #343a40);
    border-color: var(--theme-gray-800, #343a40);
    color: white;
}

.btn-dark:hover,
.btn-dark:focus {
    background-color: var(--theme-gray-900, #212529);
    border-color: var(--theme-gray-900, #212529);
    color: white;
}

/* ==================== 按钮组 ==================== */
.btn-group {
    position: relative;
    display: inline-flex;
    vertical-align: middle;
}

.btn-group > .btn {
    position: relative;
    flex: 1 1 auto;
    margin: 0;
}

.btn-group > .btn:not(:first-child) {
    margin-left: -1px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.btn-group > .btn:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.btn-group > .btn:hover,
.btn-group > .btn:focus,
.btn-group > .btn:active {
    z-index: 1;
}

/* ==================== 按钮工具栏 ==================== */
.btn-toolbar {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 8px;
}

.btn-toolbar .btn-group {
    margin-right: 0;
}

/* ==================== 企业级按钮图标 ==================== */
.btn i {
    font-size: inherit;
    line-height: inherit;
}

.btn .fa,
.btn .fas,
.btn .far,
.btn .fab {
    font-size: inherit;
}

/* 图标在文字前 */
.btn i:first-child {
    margin-right: 6px;
}

/* 图标在文字后 */
.btn i:last-child {
    margin-left: 6px;
}

/* 只有图标的按钮 */
.btn-icon-only {
    padding: 8px;
    width: auto;
    height: auto;
}

.btn-icon-only.btn-sm {
    padding: 6px;
}

.btn-icon-only.btn-lg {
    padding: 12px;
}

.btn-icon-only i {
    margin: 0;
}

/* ==================== 响应式按钮 ==================== */
@media (max-width: 768px) {
    .btn-responsive {
        width: 100%;
        margin-bottom: 8px;
    }
    
    .btn-group-responsive {
        display: flex;
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group-responsive > .btn {
        width: 100%;
        margin: 0 0 8px 0;
        border-radius: 4px !important;
    }
    
    .btn-group-responsive > .btn:last-child {
        margin-bottom: 0;
    }
}
