/* 
 * StudentsCMSSP 企业级仪表板样式
 * 专注于桌面端仪表板体验优化
 * 版本: 1.0.0
 */

/* ==================== 仪表板布局 ==================== */
.dashboard-container {
    padding: 24px;
    background-color: #f8f9fa;
    min-height: calc(100vh - 120px);
}

.dashboard-header {
    margin-bottom: 32px;
}

.dashboard-title {
    font-size: 24px;
    font-weight: 600;
    color: #2c5aa0;
    margin-bottom: 8px;
}

.dashboard-subtitle {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 0;
}

/* ==================== 统计卡片 ==================== */
.stats-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--card-accent-color, #2c5aa0);
}

.stats-card.primary::before { background: #2c5aa0; }
.stats-card.success::before { background: #198754; }
.stats-card.warning::before { background: #ffc107; }
.stats-card.danger::before { background: #dc3545; }
.stats-card.info::before { background: #0dcaf0; }

.stats-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.stats-card-title {
    font-size: 14px;
    font-weight: 500;
    color: #6c757d;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-card-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    background: var(--card-accent-color, #2c5aa0);
}

.stats-card.primary .stats-card-icon { background: #2c5aa0; }
.stats-card.success .stats-card-icon { background: #198754; }
.stats-card.warning .stats-card-icon { background: #ffc107; color: #333; }
.stats-card.danger .stats-card-icon { background: #dc3545; }
.stats-card.info .stats-card-icon { background: #0dcaf0; color: #333; }

.stats-card-value {
    font-size: 32px;
    font-weight: 700;
    color: #2c5aa0;
    margin-bottom: 8px;
    line-height: 1;
}

.stats-card-label {
    font-size: 13px;
    color: #6c757d;
    margin: 0;
}

.stats-card-footer {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: between;
    align-items: center;
}

.stats-card-change {
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.stats-card-change.positive {
    color: #198754;
}

.stats-card-change.negative {
    color: #dc3545;
}

.stats-card-link {
    color: #2c5aa0;
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
    margin-left: auto;
}

.stats-card-link:hover {
    color: #1e3d6f;
    text-decoration: underline;
}

/* ==================== 图表卡片 ==================== */
.chart-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;
    overflow: hidden;
}

.chart-card-header {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3d6f 100%);
    color: white;
    padding: 16px 24px;
    border-bottom: none;
}

.chart-card-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    color: white;
}

.chart-card-subtitle {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
    margin: 4px 0 0 0;
}

.chart-card-body {
    padding: 24px;
}

.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* ==================== 快捷操作面板 ==================== */
.quick-actions {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;
}

.quick-actions-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 16px 24px;
    border-bottom: 1px solid #e9ecef;
}

.quick-actions-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c5aa0;
    margin: 0;
}

.quick-actions-body {
    padding: 16px 24px;
}

.quick-action-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
    text-decoration: none;
    color: #333;
    transition: all 0.15s ease;
}

.quick-action-item:last-child {
    border-bottom: none;
}

.quick-action-item:hover {
    background-color: rgba(44, 90, 160, 0.04);
    color: #2c5aa0;
    text-decoration: none;
    padding-left: 8px;
}

.quick-action-icon {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    font-size: 18px;
    color: white;
    background: #2c5aa0;
}

.quick-action-content {
    flex: 1;
}

.quick-action-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin: 0 0 4px 0;
}

.quick-action-desc {
    font-size: 12px;
    color: #6c757d;
    margin: 0;
}

.quick-action-arrow {
    color: #adb5bd;
    font-size: 14px;
}

/* ==================== 最近活动面板 ==================== */
.recent-activity {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;
}

.recent-activity-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 16px 24px;
    border-bottom: 1px solid #e9ecef;
}

.recent-activity-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c5aa0;
    margin: 0;
}

.recent-activity-body {
    padding: 16px 24px;
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 14px;
    color: white;
    background: #6c757d;
    flex-shrink: 0;
}

.activity-icon.success { background: #198754; }
.activity-icon.warning { background: #ffc107; color: #333; }
.activity-icon.danger { background: #dc3545; }
.activity-icon.info { background: #0dcaf0; color: #333; }

.activity-content {
    flex: 1;
}

.activity-title {
    font-size: 13px;
    font-weight: 500;
    color: #333;
    margin: 0 0 4px 0;
}

.activity-desc {
    font-size: 12px;
    color: #6c757d;
    margin: 0 0 4px 0;
}

.activity-time {
    font-size: 11px;
    color: #adb5bd;
    margin: 0;
}

/* ==================== 响应式调整 ==================== */
@media (max-width: 1200px) {
    .dashboard-container {
        padding: 16px;
    }
    
    .stats-card {
        padding: 20px;
    }
    
    .stats-card-value {
        font-size: 28px;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 12px;
    }
    
    .stats-card {
        padding: 16px;
        margin-bottom: 16px;
    }
    
    .stats-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .stats-card-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }
    
    .stats-card-value {
        font-size: 24px;
    }
}
