/*
 * StudentsCMSSP 企业级桌面端UI核心样式
 * 完全兼容现有主题系统，使用主题变量
 * 版本: 2.0.0 - 主题集成版
 */

/* ==================== 企业级设计系统变量 ==================== */
:root {
    /* 企业级字体系统 - 13px基础 */
    --enterprise-font-size-base: 13px;
    --enterprise-font-size-sm: 12px;
    --enterprise-font-size-lg: 14px;
    --enterprise-font-size-xl: 16px;
    --enterprise-font-size-xxl: 18px;

    /* 字体权重 */
    --enterprise-font-weight-light: 300;
    --enterprise-font-weight-normal: 400;
    --enterprise-font-weight-medium: 500;
    --enterprise-font-weight-semibold: 600;
    --enterprise-font-weight-bold: 700;

    /* 行高 */
    --enterprise-line-height-base: 1.4;
    --enterprise-line-height-sm: 1.3;
    --enterprise-line-height-lg: 1.5;

    /* 边框和圆角 */
    --enterprise-border-radius: 4px;
    --enterprise-border-radius-sm: 2px;
    --enterprise-border-radius-lg: 6px;
    --enterprise-border-width: 1px;

    /* 阴影系统 - 企业级简洁 */
    --enterprise-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --enterprise-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    --enterprise-shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.12);

    /* 间距系统 */
    --enterprise-spacing-xs: 4px;
    --enterprise-spacing-sm: 8px;
    --enterprise-spacing-md: 12px;
    --enterprise-spacing-lg: 16px;
    --enterprise-spacing-xl: 24px;
    --enterprise-spacing-xxl: 32px;

    /* 过渡动画 - 企业级稳定性 */
    --enterprise-transition-fast: 0.15s ease;
    --enterprise-transition-base: 0.2s ease;
}

/* ==================== 全局字体系统 ==================== */
body {
    font-size: var(--enterprise-font-size-base) !important;
    line-height: var(--enterprise-line-height-base) !important;
    font-weight: var(--enterprise-font-weight-normal);
}

/* 标题字体 */
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
    font-weight: var(--enterprise-font-weight-semibold);
    line-height: var(--enterprise-line-height-sm);
}

h1, .h1 { font-size: var(--enterprise-font-size-xxl); }
h2, .h2 { font-size: var(--enterprise-font-size-xl); }
h3, .h3 { font-size: var(--enterprise-font-size-lg); }
h4, .h4 { font-size: var(--enterprise-font-size-base); }
h5, .h5 { font-size: var(--enterprise-font-size-sm); }
h6, .h6 { font-size: var(--enterprise-font-size-sm); }

small, .small {
    font-size: var(--enterprise-font-size-sm) !important;
}

/* ==================== 企业级卡片组件 ==================== */
.card {
    border-radius: var(--enterprise-border-radius);
    box-shadow: var(--enterprise-shadow-sm);
    transition: box-shadow var(--enterprise-transition-fast);
}

.card:hover {
    box-shadow: var(--enterprise-shadow);
}

.card-header {
    padding: var(--enterprise-spacing-md) var(--enterprise-spacing-lg);
    font-size: var(--enterprise-font-size-base);
    font-weight: var(--enterprise-font-weight-medium);
    border-radius: var(--enterprise-border-radius) var(--enterprise-border-radius) 0 0;
}

.card-header h1, .card-header h2, .card-header h3,
.card-header h4, .card-header h5, .card-header h6,
.card-header .h1, .card-header .h2, .card-header .h3,
.card-header .h4, .card-header .h5, .card-header .h6 {
    margin: 0;
    font-weight: var(--enterprise-font-weight-medium);
}

.card-title {
    margin-bottom: 0;
    font-size: var(--enterprise-font-size-base);
    font-weight: var(--enterprise-font-weight-medium);
}

.card-body {
    padding: var(--enterprise-spacing-lg);
    font-size: var(--enterprise-font-size-base);
}

.card-footer {
    background-color: var(--theme-gray-100, #f8f9fa);
    border-top: var(--enterprise-border-width) solid var(--theme-gray-200, #e9ecef);
    padding: var(--enterprise-spacing-md) var(--enterprise-spacing-lg);
    font-size: var(--enterprise-font-size-sm);
}

.card-tools {
    display: flex;
    align-items: center;
    gap: var(--enterprise-spacing-sm);
}

.card-tools .btn {
    font-size: var(--enterprise-font-size-sm);
    padding: 6px 12px;
}

/* ==================== 表格组件优化 ==================== */
.table {
    font-size: var(--enterprise-font-size-base);
    margin-bottom: 0;
    background-color: #ffffff;
}

.table th {
    background-color: var(--enterprise-gray-100);
    color: var(--enterprise-gray-800);
    font-weight: var(--enterprise-font-weight-semibold);
    font-size: var(--enterprise-font-size-base);
    padding: var(--enterprise-spacing-md);
    border-bottom: 2px solid var(--enterprise-border-color);
    border-top: none;
    vertical-align: middle;
    white-space: nowrap;
}

.table td {
    padding: var(--enterprise-spacing-md);
    vertical-align: middle;
    border-top: var(--enterprise-border-width) solid var(--enterprise-border-color);
    font-size: var(--enterprise-font-size-base);
    color: var(--enterprise-gray-800);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--enterprise-gray-50);
}

.table-hover tbody tr:hover {
    background-color: rgba(var(--enterprise-primary), 0.05);
    cursor: pointer;
}

.table-bordered {
    border: var(--enterprise-border-width) solid var(--enterprise-border-color);
}

.table-bordered th,
.table-bordered td {
    border: var(--enterprise-border-width) solid var(--enterprise-border-color);
}

/* 表格内按钮组 */
.table .btn-group-sm .btn {
    font-size: var(--enterprise-font-size-sm);
    padding: 4px 8px;
    margin: 0 1px;
}

/* 表格内徽章 */
.table .badge {
    font-size: var(--enterprise-font-size-sm);
    padding: 4px 8px;
    font-weight: var(--enterprise-font-weight-normal);
}

/* ==================== 企业级按钮系统 ==================== */
.btn {
    font-size: var(--enterprise-font-size-base);
    font-weight: var(--enterprise-font-weight-medium);
    padding: 8px 16px;
    border-radius: var(--enterprise-border-radius);
    border-width: var(--enterprise-border-width);
    transition: all var(--enterprise-transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--enterprise-spacing-xs);
    cursor: pointer;
}

.btn-sm {
    font-size: var(--enterprise-font-size-sm);
    padding: 6px 12px;
}

.btn-lg {
    font-size: var(--enterprise-font-size-lg);
    padding: 12px 24px;
}

.btn-xs {
    font-size: var(--enterprise-font-size-sm);
    padding: 4px 8px;
}

/* 企业级按钮样式 - 使用主题变量 */
.btn-primary {
    background-color: var(--theme-primary);
    border-color: var(--theme-primary);
    color: white;
}

.btn-primary:hover,
.btn-primary:focus {
    background-color: var(--theme-primary-dark);
    border-color: var(--theme-primary-dark);
    color: white;
}

.btn-outline-primary {
    color: var(--theme-primary);
    border-color: var(--theme-primary);
    background-color: transparent;
}

.btn-outline-primary:hover,
.btn-outline-primary:focus {
    background-color: var(--theme-primary);
    border-color: var(--theme-primary);
    color: white;
}

/* 按钮组 */
.btn-group .btn {
    margin: 0;
}

.btn-toolbar {
    gap: var(--enterprise-spacing-sm);
}

/* ==================== 企业级表单组件 ==================== */
.form-group {
    margin-bottom: var(--enterprise-spacing-lg);
}

.form-label,
label {
    font-size: var(--enterprise-font-size-base);
    font-weight: var(--enterprise-font-weight-medium);
    color: var(--theme-gray-800, #343a40);
    margin-bottom: var(--enterprise-spacing-xs);
    display: block;
}

.form-control {
    font-size: var(--enterprise-font-size-base);
    padding: 8px 12px;
    border-radius: var(--enterprise-border-radius);
    transition: border-color var(--enterprise-transition-fast), box-shadow var(--enterprise-transition-fast);
}

.form-control:focus {
    border-color: var(--theme-primary);
    box-shadow: 0 0 0 3px rgba(var(--theme-primary-rgb), 0.25);
    outline: none;
}

.form-control::placeholder {
    color: var(--theme-gray-500, #adb5bd);
    font-size: var(--enterprise-font-size-base);
}

/* 表单选择框 */
.form-select,
select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px 12px;
    padding-right: 32px;
}

/* 表单验证状态 */
.form-control.is-valid {
    border-color: var(--theme-success);
}

.form-control.is-invalid {
    border-color: var(--theme-danger);
}

.valid-feedback {
    color: var(--theme-success);
    font-size: var(--enterprise-font-size-sm);
}

.invalid-feedback {
    color: var(--theme-danger);
    font-size: var(--enterprise-font-size-sm);
}

/* 输入组 */
.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group-text {
    background-color: var(--theme-gray-100, #f8f9fa);
    border: var(--enterprise-border-width) solid var(--theme-gray-300, #dee2e6);
    color: var(--theme-gray-700, #495057);
    font-size: var(--enterprise-font-size-base);
    padding: 8px 12px;
}

/* ==================== 企业级导航组件 ==================== */
.sidebar {
    box-shadow: var(--enterprise-shadow-lg);
}

.sidebar-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: var(--enterprise-spacing-lg);
}

.sidebar-brand {
    font-size: var(--enterprise-font-size-lg);
    font-weight: var(--enterprise-font-weight-semibold);
    text-decoration: none;
}

.sidebar-nav .nav-link {
    font-size: var(--enterprise-font-size-base);
    padding: 10px var(--enterprise-spacing-lg);
    border-radius: var(--enterprise-border-radius);
    margin: 2px var(--enterprise-spacing-sm);
    transition: all var(--enterprise-transition-fast);
}

.sidebar-nav .nav-link:hover {
    text-decoration: none;
}

.sidebar-nav .nav-link.active {
    font-weight: var(--enterprise-font-weight-medium);
}

/* 顶部工具栏 */
.top-toolbar {
    box-shadow: var(--enterprise-shadow);
    padding: var(--enterprise-spacing-md) var(--enterprise-spacing-lg);
}

/* ==================== 企业级分页组件 ==================== */
.pagination {
    margin-bottom: 0;
}

.page-link {
    font-size: var(--enterprise-font-size-base);
    padding: 8px 12px;
    text-decoration: none;
}

/* ==================== 企业级徽章组件 ==================== */
.badge {
    font-size: var(--enterprise-font-size-sm);
    font-weight: var(--enterprise-font-weight-medium);
    padding: 4px 8px;
    border-radius: var(--enterprise-border-radius-sm);
}

/* ==================== 企业级模态框组件 ==================== */
.modal-header {
    border-bottom: none;
    padding: var(--enterprise-spacing-lg);
}

.modal-header .modal-title {
    font-size: var(--enterprise-font-size-lg);
    font-weight: var(--enterprise-font-weight-medium);
}

.modal-header .close {
    opacity: 0.8;
    font-size: var(--enterprise-font-size-xl);
}

.modal-header .close:hover {
    opacity: 1;
}

.modal-body {
    padding: var(--enterprise-spacing-lg);
    font-size: var(--enterprise-font-size-base);
}

.modal-footer {
    background-color: var(--theme-gray-100, #f8f9fa);
    border-top: var(--enterprise-border-width) solid var(--theme-gray-200, #e9ecef);
    padding: var(--enterprise-spacing-md) var(--enterprise-spacing-lg);
}

/* ==================== 企业级警告框组件 ==================== */
.alert {
    font-size: var(--enterprise-font-size-base);
    padding: var(--enterprise-spacing-md) var(--enterprise-spacing-lg);
    border-radius: var(--enterprise-border-radius);
    border-width: var(--enterprise-border-width);
}
