/* 
 * StudentsCMSSP 企业级桌面端UI优化样式
 * 基于Bootstrap框架，专注桌面端体验
 * 版本: 1.0.0
 */

/* ==================== 全局字体系统 ==================== */
:root {
    /* 企业级字体系统 - 13px基础 */
    --enterprise-font-size-base: 13px;
    --enterprise-font-size-sm: 12px;
    --enterprise-font-size-lg: 14px;
    --enterprise-font-size-xl: 16px;
    --enterprise-font-size-xxl: 18px;
    
    /* 字体权重 */
    --enterprise-font-weight-light: 300;
    --enterprise-font-weight-normal: 400;
    --enterprise-font-weight-medium: 500;
    --enterprise-font-weight-semibold: 600;
    --enterprise-font-weight-bold: 700;
    
    /* 行高 */
    --enterprise-line-height-base: 1.4;
    --enterprise-line-height-sm: 1.3;
    --enterprise-line-height-lg: 1.5;
    
    /* 企业级配色方案 */
    --enterprise-primary: #2c5aa0;
    --enterprise-primary-light: #4a7bc8;
    --enterprise-primary-dark: #1e3d6f;
    --enterprise-secondary: #6c757d;
    --enterprise-success: #198754;
    --enterprise-info: #0dcaf0;
    --enterprise-warning: #ffc107;
    --enterprise-danger: #dc3545;
    
    /* 中性色调 */
    --enterprise-gray-50: #f8f9fa;
    --enterprise-gray-100: #f1f3f4;
    --enterprise-gray-200: #e9ecef;
    --enterprise-gray-300: #dee2e6;
    --enterprise-gray-400: #ced4da;
    --enterprise-gray-500: #adb5bd;
    --enterprise-gray-600: #6c757d;
    --enterprise-gray-700: #495057;
    --enterprise-gray-800: #343a40;
    --enterprise-gray-900: #212529;
    
    /* 边框和圆角 */
    --enterprise-border-radius: 4px;
    --enterprise-border-radius-sm: 2px;
    --enterprise-border-radius-lg: 6px;
    --enterprise-border-width: 1px;
    --enterprise-border-color: var(--enterprise-gray-300);
    
    /* 阴影系统 */
    --enterprise-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --enterprise-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --enterprise-shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.15);
    --enterprise-shadow-xl: 0 8px 16px rgba(0, 0, 0, 0.2);
    
    /* 间距系统 */
    --enterprise-spacing-xs: 4px;
    --enterprise-spacing-sm: 8px;
    --enterprise-spacing-md: 12px;
    --enterprise-spacing-lg: 16px;
    --enterprise-spacing-xl: 24px;
    --enterprise-spacing-xxl: 32px;
}

/* 全局字体设置 */
body {
    font-size: var(--enterprise-font-size-base) !important;
    line-height: var(--enterprise-line-height-base) !important;
    font-weight: var(--enterprise-font-weight-normal);
    color: var(--enterprise-gray-800);
}

/* 标题字体 */
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
    font-weight: var(--enterprise-font-weight-semibold);
    color: var(--enterprise-gray-900);
    line-height: var(--enterprise-line-height-sm);
}

h1, .h1 { font-size: var(--enterprise-font-size-xxl); }
h2, .h2 { font-size: var(--enterprise-font-size-xl); }
h3, .h3 { font-size: var(--enterprise-font-size-lg); }
h4, .h4 { font-size: var(--enterprise-font-size-base); }
h5, .h5 { font-size: var(--enterprise-font-size-sm); }
h6, .h6 { font-size: var(--enterprise-font-size-sm); }

/* 小字体 */
small, .small {
    font-size: var(--enterprise-font-size-sm) !important;
}

/* ==================== 卡片组件优化 ==================== */
.card {
    border: var(--enterprise-border-width) solid var(--enterprise-border-color);
    border-radius: var(--enterprise-border-radius);
    box-shadow: var(--enterprise-shadow-sm);
    background-color: #ffffff;
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: var(--enterprise-shadow);
}

.card-header {
    background: linear-gradient(135deg, var(--enterprise-primary) 0%, var(--enterprise-primary-dark) 100%);
    color: white;
    border-bottom: none;
    padding: var(--enterprise-spacing-md) var(--enterprise-spacing-lg);
    font-size: var(--enterprise-font-size-base);
    font-weight: var(--enterprise-font-weight-medium);
    border-radius: var(--enterprise-border-radius) var(--enterprise-border-radius) 0 0;
}

.card-header h1, .card-header h2, .card-header h3, 
.card-header h4, .card-header h5, .card-header h6,
.card-header .h1, .card-header .h2, .card-header .h3,
.card-header .h4, .card-header .h5, .card-header .h6 {
    color: white !important;
    margin: 0;
    font-weight: var(--enterprise-font-weight-medium);
}

.card-title {
    margin-bottom: 0;
    font-size: var(--enterprise-font-size-base);
    font-weight: var(--enterprise-font-weight-medium);
}

.card-body {
    padding: var(--enterprise-spacing-lg);
    font-size: var(--enterprise-font-size-base);
}

.card-footer {
    background-color: var(--enterprise-gray-50);
    border-top: var(--enterprise-border-width) solid var(--enterprise-border-color);
    padding: var(--enterprise-spacing-md) var(--enterprise-spacing-lg);
    font-size: var(--enterprise-font-size-sm);
}

/* 卡片工具栏 */
.card-tools {
    display: flex;
    align-items: center;
    gap: var(--enterprise-spacing-sm);
}

.card-tools .btn {
    font-size: var(--enterprise-font-size-sm);
    padding: 6px 12px;
}

/* ==================== 表格组件优化 ==================== */
.table {
    font-size: var(--enterprise-font-size-base);
    margin-bottom: 0;
    background-color: #ffffff;
}

.table th {
    background-color: var(--enterprise-gray-100);
    color: var(--enterprise-gray-800);
    font-weight: var(--enterprise-font-weight-semibold);
    font-size: var(--enterprise-font-size-base);
    padding: var(--enterprise-spacing-md);
    border-bottom: 2px solid var(--enterprise-border-color);
    border-top: none;
    vertical-align: middle;
    white-space: nowrap;
}

.table td {
    padding: var(--enterprise-spacing-md);
    vertical-align: middle;
    border-top: var(--enterprise-border-width) solid var(--enterprise-border-color);
    font-size: var(--enterprise-font-size-base);
    color: var(--enterprise-gray-800);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--enterprise-gray-50);
}

.table-hover tbody tr:hover {
    background-color: rgba(var(--enterprise-primary), 0.05);
    cursor: pointer;
}

.table-bordered {
    border: var(--enterprise-border-width) solid var(--enterprise-border-color);
}

.table-bordered th,
.table-bordered td {
    border: var(--enterprise-border-width) solid var(--enterprise-border-color);
}

/* 表格内按钮组 */
.table .btn-group-sm .btn {
    font-size: var(--enterprise-font-size-sm);
    padding: 4px 8px;
    margin: 0 1px;
}

/* 表格内徽章 */
.table .badge {
    font-size: var(--enterprise-font-size-sm);
    padding: 4px 8px;
    font-weight: var(--enterprise-font-weight-normal);
}

/* ==================== 按钮组件优化 ==================== */
.btn {
    font-size: var(--enterprise-font-size-base);
    font-weight: var(--enterprise-font-weight-medium);
    padding: 8px 16px;
    border-radius: var(--enterprise-border-radius);
    border-width: var(--enterprise-border-width);
    transition: all 0.15s ease-in-out;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--enterprise-spacing-xs);
}

.btn-sm {
    font-size: var(--enterprise-font-size-sm);
    padding: 6px 12px;
}

.btn-lg {
    font-size: var(--enterprise-font-size-lg);
    padding: 12px 24px;
}

/* 主要按钮 */
.btn-primary {
    background: linear-gradient(135deg, var(--enterprise-primary) 0%, var(--enterprise-primary-dark) 100%);
    border-color: var(--enterprise-primary);
    color: white;
    box-shadow: var(--enterprise-shadow-sm);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--enterprise-primary-dark) 0%, var(--enterprise-primary) 100%);
    border-color: var(--enterprise-primary-dark);
    color: white;
    box-shadow: var(--enterprise-shadow);
    transform: translateY(-1px);
}

/* 次要按钮 */
.btn-secondary {
    background-color: var(--enterprise-secondary);
    border-color: var(--enterprise-secondary);
    color: white;
}

.btn-outline-primary {
    color: var(--enterprise-primary);
    border-color: var(--enterprise-primary);
    background-color: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--enterprise-primary);
    border-color: var(--enterprise-primary);
    color: white;
}

/* 按钮组优化 */
.btn-group .btn {
    margin: 0;
}

.btn-toolbar {
    gap: var(--enterprise-spacing-sm);
}

/* ==================== 表单组件优化 ==================== */
.form-group {
    margin-bottom: var(--enterprise-spacing-lg);
}

.form-label,
label {
    font-size: var(--enterprise-font-size-base);
    font-weight: var(--enterprise-font-weight-medium);
    color: var(--enterprise-gray-800);
    margin-bottom: var(--enterprise-spacing-xs);
    display: block;
}

.form-control {
    font-size: var(--enterprise-font-size-base);
    padding: 8px 12px;
    border: var(--enterprise-border-width) solid var(--enterprise-border-color);
    border-radius: var(--enterprise-border-radius);
    background-color: #ffffff;
    color: var(--enterprise-gray-800);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: var(--enterprise-primary);
    box-shadow: 0 0 0 3px rgba(var(--enterprise-primary), 0.1);
    outline: none;
}

.form-control::placeholder {
    color: var(--enterprise-gray-500);
    font-size: var(--enterprise-font-size-base);
}

/* 表单选择框 */
.form-select,
select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px 12px;
    padding-right: 32px;
}

/* 表单验证状态 */
.form-control.is-valid {
    border-color: var(--enterprise-success);
}

.form-control.is-invalid {
    border-color: var(--enterprise-danger);
}

.valid-feedback {
    color: var(--enterprise-success);
    font-size: var(--enterprise-font-size-sm);
}

.invalid-feedback {
    color: var(--enterprise-danger);
    font-size: var(--enterprise-font-size-sm);
}

/* 输入组 */
.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group-text {
    background-color: var(--enterprise-gray-100);
    border: var(--enterprise-border-width) solid var(--enterprise-border-color);
    color: var(--enterprise-gray-700);
    font-size: var(--enterprise-font-size-base);
    padding: 8px 12px;
}

/* ==================== 导航组件优化 ==================== */
.sidebar {
    background: linear-gradient(180deg, var(--enterprise-primary) 0%, var(--enterprise-primary-dark) 100%);
    box-shadow: var(--enterprise-shadow-lg);
}

.sidebar-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: var(--enterprise-spacing-lg);
}

.sidebar-brand {
    font-size: var(--enterprise-font-size-lg);
    font-weight: var(--enterprise-font-weight-semibold);
    color: white;
    text-decoration: none;
}

.sidebar-nav .nav-link {
    font-size: var(--enterprise-font-size-base);
    padding: 10px var(--enterprise-spacing-lg);
    color: rgba(255, 255, 255, 0.9);
    border-radius: var(--enterprise-border-radius);
    margin: 2px var(--enterprise-spacing-sm);
    transition: all 0.15s ease-in-out;
}

.sidebar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
}

.sidebar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: var(--enterprise-font-weight-medium);
}

/* 顶部工具栏 */
.top-toolbar {
    background: linear-gradient(135deg, var(--enterprise-primary) 0%, var(--enterprise-primary-dark) 100%);
    box-shadow: var(--enterprise-shadow);
    padding: var(--enterprise-spacing-md) var(--enterprise-spacing-lg);
}

/* ==================== 分页组件优化 ==================== */
.pagination {
    margin-bottom: 0;
}

.page-link {
    font-size: var(--enterprise-font-size-base);
    padding: 8px 12px;
    color: var(--enterprise-primary);
    border: var(--enterprise-border-width) solid var(--enterprise-border-color);
    background-color: #ffffff;
    text-decoration: none;
}

.page-link:hover {
    color: var(--enterprise-primary-dark);
    background-color: var(--enterprise-gray-100);
    border-color: var(--enterprise-border-color);
}

.page-item.active .page-link {
    background-color: var(--enterprise-primary);
    border-color: var(--enterprise-primary);
    color: white;
}

.page-item.disabled .page-link {
    color: var(--enterprise-gray-500);
    background-color: var(--enterprise-gray-100);
    border-color: var(--enterprise-border-color);
}

/* ==================== 徽章组件优化 ==================== */
.badge {
    font-size: var(--enterprise-font-size-sm);
    font-weight: var(--enterprise-font-weight-medium);
    padding: 4px 8px;
    border-radius: var(--enterprise-border-radius-sm);
}

.badge-primary {
    background-color: var(--enterprise-primary);
    color: white;
}

.badge-success {
    background-color: var(--enterprise-success);
    color: white;
}

.badge-warning {
    background-color: var(--enterprise-warning);
    color: var(--enterprise-gray-800);
}

.badge-danger {
    background-color: var(--enterprise-danger);
    color: white;
}

.badge-info {
    background-color: var(--enterprise-info);
    color: white;
}

.badge-secondary {
    background-color: var(--enterprise-secondary);
    color: white;
}

/* ==================== 模态框组件优化 ==================== */
.modal-header {
    background: linear-gradient(135deg, var(--enterprise-primary) 0%, var(--enterprise-primary-dark) 100%);
    color: white;
    border-bottom: none;
    padding: var(--enterprise-spacing-lg);
}

.modal-header .modal-title {
    font-size: var(--enterprise-font-size-lg);
    font-weight: var(--enterprise-font-weight-medium);
    color: white;
}

.modal-header .close {
    color: white;
    opacity: 0.8;
    font-size: var(--enterprise-font-size-xl);
}

.modal-header .close:hover {
    opacity: 1;
}

.modal-body {
    padding: var(--enterprise-spacing-lg);
    font-size: var(--enterprise-font-size-base);
}

.modal-footer {
    background-color: var(--enterprise-gray-50);
    border-top: var(--enterprise-border-width) solid var(--enterprise-border-color);
    padding: var(--enterprise-spacing-md) var(--enterprise-spacing-lg);
}

/* ==================== 警告框组件优化 ==================== */
.alert {
    font-size: var(--enterprise-font-size-base);
    padding: var(--enterprise-spacing-md) var(--enterprise-spacing-lg);
    border-radius: var(--enterprise-border-radius);
    border-width: var(--enterprise-border-width);
}

.alert-primary {
    background-color: rgba(var(--enterprise-primary), 0.1);
    border-color: var(--enterprise-primary);
    color: var(--enterprise-primary-dark);
}

.alert-success {
    background-color: rgba(var(--enterprise-success), 0.1);
    border-color: var(--enterprise-success);
    color: var(--enterprise-success);
}

.alert-warning {
    background-color: rgba(var(--enterprise-warning), 0.1);
    border-color: var(--enterprise-warning);
    color: var(--enterprise-warning);
}

.alert-danger {
    background-color: rgba(var(--enterprise-danger), 0.1);
    border-color: var(--enterprise-danger);
    color: var(--enterprise-danger);
}

.alert-info {
    background-color: rgba(var(--enterprise-info), 0.1);
    border-color: var(--enterprise-info);
    color: var(--enterprise-info);
}
