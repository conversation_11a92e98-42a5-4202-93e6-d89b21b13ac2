/* 
 * StudentsCMSSP 企业级表单样式
 * 专注于桌面端表单体验优化
 * 版本: 1.0.0
 */

/* ==================== 表单容器 ==================== */
.enterprise-form {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    padding: 24px;
    margin-bottom: 24px;
}

.enterprise-form-header {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3d6f 100%);
    color: white;
    padding: 16px 24px;
    margin: -24px -24px 24px -24px;
    border-radius: 8px 8px 0 0;
}

.enterprise-form-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    color: white;
}

.enterprise-form-subtitle {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
    margin: 4px 0 0 0;
}

/* ==================== 表单分组 ==================== */
.form-section {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #f1f3f4;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section-title {
    font-size: 14px;
    font-weight: 600;
    color: #2c5aa0;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-section-title i {
    color: #2c5aa0;
}

/* ==================== 表单字段 ==================== */
.form-group {
    margin-bottom: 20px;
}

.form-group.required .form-label::after {
    content: ' *';
    color: #dc3545;
    font-weight: bold;
}

.form-label {
    font-size: 13px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 6px;
    display: block;
    line-height: 1.4;
}

.form-label-help {
    font-size: 12px;
    color: #6c757d;
    font-weight: normal;
    margin-left: 4px;
}

/* ==================== 输入控件 ==================== */
.form-control {
    font-size: 13px;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: #ffffff;
    color: #495057;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    line-height: 1.4;
}

.form-control:focus {
    border-color: #2c5aa0;
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
    outline: none;
    background-color: #ffffff;
}

.form-control::placeholder {
    color: #adb5bd;
    font-size: 13px;
}

.form-control:disabled,
.form-control[readonly] {
    background-color: #f8f9fa;
    border-color: #e9ecef;
    color: #6c757d;
}

/* 小尺寸输入框 */
.form-control-sm {
    font-size: 12px;
    padding: 6px 8px;
}

/* 大尺寸输入框 */
.form-control-lg {
    font-size: 14px;
    padding: 12px 16px;
}

/* ==================== 选择框 ==================== */
.form-select {
    font-size: 13px;
    padding: 10px 32px 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: #ffffff;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px 12px;
    appearance: none;
}

.form-select:focus {
    border-color: #2c5aa0;
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
    outline: none;
}

/* ==================== 文本域 ==================== */
.form-textarea {
    min-height: 80px;
    resize: vertical;
}

.form-textarea-lg {
    min-height: 120px;
}

/* ==================== 输入组 ==================== */
.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group-text {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    color: #495057;
    font-size: 13px;
    font-weight: 500;
    padding: 10px 12px;
    white-space: nowrap;
    border-radius: 4px;
}

.input-group .form-control {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
}

.input-group-prepend .input-group-text {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: 0;
}

.input-group-append .input-group-text {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0;
}

.input-group-prepend + .form-control {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.form-control + .input-group-append .input-group-text {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

/* ==================== 复选框和单选框 ==================== */
.form-check {
    position: relative;
    display: block;
    padding-left: 24px;
    margin-bottom: 8px;
}

.form-check-input {
    position: absolute;
    margin-top: 2px;
    margin-left: -24px;
    width: 16px;
    height: 16px;
}

.form-check-label {
    font-size: 13px;
    color: #495057;
    cursor: pointer;
}

.form-check-inline {
    display: inline-flex;
    align-items: center;
    padding-left: 0;
    margin-right: 16px;
}

.form-check-inline .form-check-input {
    position: static;
    margin-top: 0;
    margin-right: 6px;
    margin-left: 0;
}

/* ==================== 表单验证 ==================== */
.form-control.is-valid {
    border-color: #198754;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 2.94-2.94.94-.94.94.94L2.3 8.5z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px 16px;
}

.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4m0-2.4L5.8 7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px 16px;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 4px;
    font-size: 12px;
    color: #198754;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 4px;
    font-size: 12px;
    color: #dc3545;
}

/* ==================== 表单帮助文本 ==================== */
.form-text {
    margin-top: 4px;
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

.form-text.text-muted {
    color: #6c757d;
}

/* ==================== 表单按钮组 ==================== */
.form-actions {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.form-actions.centered {
    justify-content: center;
}

.form-actions.left {
    justify-content: flex-start;
}

.form-actions.space-between {
    justify-content: space-between;
}

/* ==================== 响应式布局 ==================== */
@media (max-width: 768px) {
    .enterprise-form {
        padding: 16px;
        margin-bottom: 16px;
    }
    
    .enterprise-form-header {
        padding: 12px 16px;
        margin: -16px -16px 16px -16px;
    }
    
    .form-section {
        margin-bottom: 24px;
        padding-bottom: 16px;
    }
    
    .form-group {
        margin-bottom: 16px;
    }
    
    .form-actions {
        margin-top: 24px;
        padding-top: 16px;
        flex-direction: column;
    }
    
    .form-actions .btn {
        width: 100%;
        margin-bottom: 8px;
    }
}

/* ==================== 特殊表单元素 ==================== */
.form-floating {
    position: relative;
}

.form-floating > .form-control {
    height: 48px;
    padding: 16px 12px 4px 12px;
}

.form-floating > label {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    padding: 16px 12px;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* 文件上传 */
.form-file {
    position: relative;
    display: inline-block;
    width: 100%;
}

.form-file-input {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 40px;
    margin: 0;
    opacity: 0;
}

.form-file-label {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1;
    height: 40px;
    padding: 10px 12px;
    font-size: 13px;
    font-weight: 400;
    line-height: 1.4;
    color: #495057;
    background-color: #ffffff;
    border: 1px solid #ced4da;
    border-radius: 4px;
    cursor: pointer;
}

.form-file-label::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 3;
    display: block;
    height: 38px;
    padding: 10px 12px;
    line-height: 1.4;
    color: #495057;
    content: "浏览";
    background-color: #f8f9fa;
    border-left: inherit;
    border-radius: 0 4px 4px 0;
}
