/* 
 * StudentsCMSSP 企业级表格增强样式
 * 专注于桌面端表格体验优化
 * 版本: 1.0.0
 */

/* ==================== 企业级表格基础样式 ==================== */
.enterprise-table-container {
    background: #ffffff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 24px;
}

.enterprise-table {
    width: 100%;
    margin-bottom: 0;
    font-size: 13px;
    color: #333;
    background-color: #ffffff;
    border-collapse: separate;
    border-spacing: 0;
}

/* 表格头部样式 */
.enterprise-table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    font-weight: 600;
    font-size: 13px;
    padding: 12px 16px;
    border-bottom: 2px solid #dee2e6;
    border-right: 1px solid #dee2e6;
    text-align: left;
    vertical-align: middle;
    white-space: nowrap;
    position: relative;
}

.enterprise-table thead th:last-child {
    border-right: none;
}

.enterprise-table thead th:first-child {
    border-top-left-radius: 6px;
}

.enterprise-table thead th:last-child {
    border-top-right-radius: 6px;
}

/* 表格头部排序图标 */
.enterprise-table thead th.sortable {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.15s ease;
}

.enterprise-table thead th.sortable:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.enterprise-table thead th.sortable::after {
    content: '\f0dc';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.5;
    font-size: 11px;
}

.enterprise-table thead th.sort-asc::after {
    content: '\f0de';
    opacity: 1;
    color: #007bff;
}

.enterprise-table thead th.sort-desc::after {
    content: '\f0dd';
    opacity: 1;
    color: #007bff;
}

/* 表格主体样式 */
.enterprise-table tbody tr {
    transition: background-color 0.15s ease;
    border-bottom: 1px solid #f1f3f4;
}

.enterprise-table tbody tr:hover {
    background-color: rgba(44, 90, 160, 0.04);
}

.enterprise-table tbody tr:nth-child(even) {
    background-color: #fafbfc;
}

.enterprise-table tbody tr:nth-child(even):hover {
    background-color: rgba(44, 90, 160, 0.06);
}

.enterprise-table tbody td {
    padding: 12px 16px;
    vertical-align: middle;
    border-right: 1px solid #f1f3f4;
    font-size: 13px;
    color: #333;
    line-height: 1.4;
}

.enterprise-table tbody td:last-child {
    border-right: none;
}

/* 表格内容对齐 */
.enterprise-table .text-center {
    text-align: center;
}

.enterprise-table .text-right {
    text-align: right;
}

.enterprise-table .text-left {
    text-align: left;
}

/* 数字列样式 */
.enterprise-table .number-col {
    text-align: right;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-weight: 500;
}

/* 金额列样式 */
.enterprise-table .amount-col {
    text-align: right;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-weight: 500;
    color: #2c5aa0;
}

.enterprise-table .amount-col::before {
    content: '¥';
    margin-right: 2px;
    color: #666;
}

/* 日期列样式 */
.enterprise-table .date-col {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    color: #666;
}

/* 状态列样式 */
.enterprise-table .status-col {
    text-align: center;
}

/* 操作列样式 */
.enterprise-table .action-col {
    text-align: center;
    white-space: nowrap;
}

.enterprise-table .action-col .btn {
    margin: 0 2px;
    padding: 4px 8px;
    font-size: 12px;
}

.enterprise-table .action-col .btn-group {
    display: inline-flex;
}

/* ==================== 表格内组件样式 ==================== */

/* 表格内按钮 */
.enterprise-table .btn-sm {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
    font-weight: 500;
}

.enterprise-table .btn-xs {
    padding: 2px 6px;
    font-size: 11px;
    border-radius: 2px;
}

/* 表格内徽章 */
.enterprise-table .badge {
    font-size: 11px;
    padding: 4px 8px;
    font-weight: 500;
    border-radius: 3px;
}

.enterprise-table .badge-success {
    background-color: #198754;
    color: white;
}

.enterprise-table .badge-warning {
    background-color: #ffc107;
    color: #333;
}

.enterprise-table .badge-danger {
    background-color: #dc3545;
    color: white;
}

.enterprise-table .badge-info {
    background-color: #0dcaf0;
    color: #333;
}

.enterprise-table .badge-secondary {
    background-color: #6c757d;
    color: white;
}

.enterprise-table .badge-primary {
    background-color: #2c5aa0;
    color: white;
}

/* 表格内链接 */
.enterprise-table a {
    color: #2c5aa0;
    text-decoration: none;
    font-weight: 500;
}

.enterprise-table a:hover {
    color: #1e3d6f;
    text-decoration: underline;
}

/* 表格内图片 */
.enterprise-table .table-img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

/* ==================== 表格响应式处理 ==================== */
.table-responsive {
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* ==================== 表格工具栏 ==================== */
.table-toolbar {
    background: #f8f9fa;
    padding: 12px 16px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-toolbar-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.table-toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.table-toolbar .btn {
    font-size: 12px;
    padding: 6px 12px;
}

/* 表格搜索框 */
.table-search {
    position: relative;
    max-width: 300px;
}

.table-search input {
    padding-left: 32px;
    font-size: 13px;
}

.table-search .search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 14px;
}

/* ==================== 表格分页样式 ==================== */
.table-pagination {
    background: #f8f9fa;
    padding: 12px 16px;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-pagination .pagination {
    margin: 0;
}

.table-pagination .pagination-info {
    font-size: 13px;
    color: #6c757d;
}

/* ==================== 表格加载状态 ==================== */
.table-loading {
    position: relative;
}

.table-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.table-loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #2c5aa0;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==================== 表格空状态 ==================== */
.table-empty {
    text-align: center;
    padding: 48px 24px;
    color: #6c757d;
}

.table-empty-icon {
    font-size: 48px;
    color: #dee2e6;
    margin-bottom: 16px;
}

.table-empty-text {
    font-size: 16px;
    margin-bottom: 8px;
}

.table-empty-subtext {
    font-size: 14px;
    color: #adb5bd;
}
