/* 
 * StudentsCMSSP 企业级表格修正样式
 * 解决表头分布、下拉框显示、按钮优化等问题
 * 与主题系统完全兼容
 * 版本: 1.0.0
 */

/* ==================== 表格表头修正 ==================== */
/* 统一表头样式，按列分布背景色 */
.enterprise-table thead th,
.table thead th {
    background: linear-gradient(135deg, var(--theme-gray-100, #f8f9fa) 0%, var(--theme-gray-200, #e9ecef) 100%);
    color: var(--theme-gray-800, #343a40);
    font-weight: 600;
    font-size: 13px !important;
    padding: 12px 16px;
    border-bottom: 2px solid var(--theme-gray-300, #dee2e6);
    border-right: 1px solid var(--theme-gray-300, #dee2e6);
    text-align: left;
    vertical-align: middle;
    white-space: nowrap;
    position: relative;
    line-height: 1.4;
}

.enterprise-table thead th:last-child,
.table thead th:last-child {
    border-right: none;
}

.enterprise-table thead th:first-child,
.table thead th:first-child {
    border-top-left-radius: 6px;
}

.enterprise-table thead th:last-child,
.table thead th:last-child {
    border-top-right-radius: 6px;
}

/* 表格主体统一样式 */
.enterprise-table tbody tr,
.table tbody tr {
    transition: background-color 0.15s ease;
    border-bottom: 1px solid var(--theme-gray-200, #e9ecef);
}

.enterprise-table tbody tr:hover,
.table tbody tr:hover {
    background-color: rgba(var(--theme-primary-rgb, 0, 123, 255), 0.04);
}

.enterprise-table tbody tr:nth-child(even),
.table tbody tr:nth-child(even) {
    background-color: var(--theme-gray-100, #f8f9fa);
}

.enterprise-table tbody tr:nth-child(even):hover,
.table tbody tr:nth-child(even):hover {
    background-color: rgba(var(--theme-primary-rgb, 0, 123, 255), 0.06);
}

.enterprise-table tbody td,
.table tbody td {
    padding: 12px 16px;
    vertical-align: middle;
    border-right: 1px solid var(--theme-gray-200, #e9ecef);
    font-size: 13px !important;
    color: var(--theme-gray-800, #343a40);
    line-height: 1.4;
}

.enterprise-table tbody td:last-child,
.table tbody td:last-child {
    border-right: none;
}

/* ==================== 下拉框修正 ==================== */
/* 修正下拉框文字显示问题 */
.form-select,
select.form-control,
.custom-select {
    font-size: 13px !important;
    padding: 8px 32px 8px 12px !important;
    border: 1px solid var(--theme-gray-300, #ced4da);
    border-radius: 4px;
    background-color: #ffffff;
    color: var(--theme-gray-800, #343a40);
    line-height: 1.4 !important;
    height: auto !important;
    min-height: 38px;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px 12px;
    appearance: none;
    word-wrap: normal;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.form-select:focus,
select.form-control:focus,
.custom-select:focus {
    border-color: var(--theme-primary, #007bff);
    box-shadow: 0 0 0 3px rgba(var(--theme-primary-rgb, 0, 123, 255), 0.1);
    outline: none;
}

/* 下拉框选项样式 */
.form-select option,
select.form-control option,
.custom-select option {
    font-size: 13px;
    padding: 8px 12px;
    color: var(--theme-gray-800, #343a40);
    background-color: #ffffff;
}

/* ==================== 按钮统一优化 ==================== */
/* 所有按钮的统一样式 */
.btn {
    font-size: 13px !important;
    font-weight: 500 !important;
    padding: 8px 16px !important;
    border-radius: 4px !important;
    border-width: 1px !important;
    transition: all 0.15s ease-in-out !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 4px !important;
    line-height: 1.4 !important;
    white-space: nowrap !important;
}

.btn-sm {
    font-size: 12px !important;
    padding: 6px 12px !important;
}

.btn-xs {
    font-size: 11px !important;
    padding: 4px 8px !important;
}

.btn-lg {
    font-size: 14px !important;
    padding: 12px 24px !important;
}

/* 主要按钮 - 使用主题颜色 */
.btn-primary {
    background-color: var(--theme-primary, #007bff) !important;
    border-color: var(--theme-primary, #007bff) !important;
    color: white !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.btn-primary:hover {
    background-color: var(--theme-primary-dark, #0056b3) !important;
    border-color: var(--theme-primary-dark, #0056b3) !important;
    color: white !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.btn-primary:focus {
    box-shadow: 0 0 0 3px rgba(var(--theme-primary-rgb, 0, 123, 255), 0.25) !important;
}

/* 次要按钮 */
.btn-secondary {
    background-color: var(--theme-secondary, #6c757d) !important;
    border-color: var(--theme-secondary, #6c757d) !important;
    color: white !important;
}

.btn-secondary:hover {
    background-color: var(--theme-secondary-dark, #545b62) !important;
    border-color: var(--theme-secondary-dark, #545b62) !important;
    color: white !important;
}

/* 成功按钮 */
.btn-success {
    background-color: var(--theme-success, #28a745) !important;
    border-color: var(--theme-success, #28a745) !important;
    color: white !important;
}

.btn-success:hover {
    background-color: var(--theme-success-dark, #1e7e34) !important;
    border-color: var(--theme-success-dark, #1e7e34) !important;
    color: white !important;
}

/* 警告按钮 */
.btn-warning {
    background-color: var(--theme-warning, #ffc107) !important;
    border-color: var(--theme-warning, #ffc107) !important;
    color: var(--theme-gray-800, #343a40) !important;
}

.btn-warning:hover {
    background-color: var(--theme-warning-dark, #e0a800) !important;
    border-color: var(--theme-warning-dark, #e0a800) !important;
    color: var(--theme-gray-800, #343a40) !important;
}

/* 危险按钮 */
.btn-danger {
    background-color: var(--theme-danger, #dc3545) !important;
    border-color: var(--theme-danger, #dc3545) !important;
    color: white !important;
}

.btn-danger:hover {
    background-color: var(--theme-danger-dark, #bd2130) !important;
    border-color: var(--theme-danger-dark, #bd2130) !important;
    color: white !important;
}

/* 信息按钮 */
.btn-info {
    background-color: var(--theme-info, #17a2b8) !important;
    border-color: var(--theme-info, #17a2b8) !important;
    color: white !important;
}

.btn-info:hover {
    background-color: var(--theme-info-dark, #117a8b) !important;
    border-color: var(--theme-info-dark, #117a8b) !important;
    color: white !important;
}

/* 轮廓按钮 */
.btn-outline-primary {
    color: var(--theme-primary, #007bff) !important;
    border-color: var(--theme-primary, #007bff) !important;
    background-color: transparent !important;
}

.btn-outline-primary:hover {
    background-color: var(--theme-primary, #007bff) !important;
    border-color: var(--theme-primary, #007bff) !important;
    color: white !important;
}

.btn-outline-secondary {
    color: var(--theme-secondary, #6c757d) !important;
    border-color: var(--theme-secondary, #6c757d) !important;
    background-color: transparent !important;
}

.btn-outline-secondary:hover {
    background-color: var(--theme-secondary, #6c757d) !important;
    border-color: var(--theme-secondary, #6c757d) !important;
    color: white !important;
}

.btn-outline-success {
    color: var(--theme-success, #28a745) !important;
    border-color: var(--theme-success, #28a745) !important;
    background-color: transparent !important;
}

.btn-outline-success:hover {
    background-color: var(--theme-success, #28a745) !important;
    border-color: var(--theme-success, #28a745) !important;
    color: white !important;
}

.btn-outline-warning {
    color: var(--theme-warning, #ffc107) !important;
    border-color: var(--theme-warning, #ffc107) !important;
    background-color: transparent !important;
}

.btn-outline-warning:hover {
    background-color: var(--theme-warning, #ffc107) !important;
    border-color: var(--theme-warning, #ffc107) !important;
    color: var(--theme-gray-800, #343a40) !important;
}

.btn-outline-danger {
    color: var(--theme-danger, #dc3545) !important;
    border-color: var(--theme-danger, #dc3545) !important;
    background-color: transparent !important;
}

.btn-outline-danger:hover {
    background-color: var(--theme-danger, #dc3545) !important;
    border-color: var(--theme-danger, #dc3545) !important;
    color: white !important;
}

.btn-outline-info {
    color: var(--theme-info, #17a2b8) !important;
    border-color: var(--theme-info, #17a2b8) !important;
    background-color: transparent !important;
}

.btn-outline-info:hover {
    background-color: var(--theme-info, #17a2b8) !important;
    border-color: var(--theme-info, #17a2b8) !important;
    color: white !important;
}

/* 按钮组优化 */
.btn-group .btn {
    margin: 0 !important;
}

.btn-toolbar {
    gap: 8px;
}

/* ==================== 表单控件修正 ==================== */
/* 输入框统一样式 */
.form-control {
    font-size: 13px !important;
    padding: 8px 12px !important;
    border: 1px solid var(--theme-gray-300, #ced4da) !important;
    border-radius: 4px !important;
    background-color: #ffffff !important;
    color: var(--theme-gray-800, #343a40) !important;
    line-height: 1.4 !important;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
}

.form-control:focus {
    border-color: var(--theme-primary, #007bff) !important;
    box-shadow: 0 0 0 3px rgba(var(--theme-primary-rgb, 0, 123, 255), 0.1) !important;
    outline: none !important;
}

.form-control::placeholder {
    color: var(--theme-gray-500, #adb5bd) !important;
    font-size: 13px !important;
}

/* 标签统一样式 */
.form-label,
label {
    font-size: 13px !important;
    font-weight: 500 !important;
    color: var(--theme-gray-800, #343a40) !important;
    margin-bottom: 6px !important;
    display: block !important;
    line-height: 1.4 !important;
}

/* ==================== 徽章修正 ==================== */
.badge {
    font-size: 12px !important;
    font-weight: 500 !important;
    padding: 4px 8px !important;
    border-radius: 3px !important;
    line-height: 1.2 !important;
}

.badge-primary {
    background-color: var(--theme-primary, #007bff) !important;
    color: white !important;
}

.badge-secondary {
    background-color: var(--theme-secondary, #6c757d) !important;
    color: white !important;
}

.badge-success {
    background-color: var(--theme-success, #28a745) !important;
    color: white !important;
}

.badge-warning {
    background-color: var(--theme-warning, #ffc107) !important;
    color: var(--theme-gray-800, #343a40) !important;
}

.badge-danger {
    background-color: var(--theme-danger, #dc3545) !important;
    color: white !important;
}

.badge-info {
    background-color: var(--theme-info, #17a2b8) !important;
    color: white !important;
}

.badge-light {
    background-color: var(--theme-gray-100, #f8f9fa) !important;
    color: var(--theme-gray-800, #343a40) !important;
    border: 1px solid var(--theme-gray-300, #dee2e6) !important;
}
