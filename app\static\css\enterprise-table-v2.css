/* 
 * StudentsCMSSP 企业级表格系统 V2
 * 现代化设计，与主题系统完美集成
 * 版本: 2.0.0 - 高级企业版
 */

/* ==================== 现代表格容器 ==================== */
.enterprise-table-v2-container {
    background: var(--theme-surface, #ffffff);
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 24px;
    border: 1px solid var(--theme-gray-200, #e9ecef);
    transition: box-shadow 0.2s ease;
}

.enterprise-table-v2-container:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* ==================== 智能工具栏 ==================== */
.smart-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: linear-gradient(135deg, var(--theme-gray-50, #f8f9fa) 0%, var(--theme-gray-100, #f1f3f4) 100%);
    border-bottom: 1px solid var(--theme-gray-200, #e9ecef);
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 搜索表单集成 */
.toolbar-search-form {
    display: flex;
    align-items: end;
    gap: 12px;
    flex: 1;
    max-width: 800px;
}

.toolbar-search-form .form-group {
    margin-bottom: 0;
}

.toolbar-search-form .form-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--theme-gray-600, #6c757d);
    margin-bottom: 4px;
}

.toolbar-search-form .form-control,
.toolbar-search-form .form-select {
    font-size: 13px;
    padding: 6px 12px;
    border-radius: 6px;
    border: 1px solid var(--theme-gray-300, #dee2e6);
    transition: all 0.15s ease;
}

.toolbar-search-form .form-control:focus,
.toolbar-search-form .form-select:focus {
    border-color: var(--theme-primary);
    box-shadow: 0 0 0 3px rgba(var(--theme-primary-rgb), 0.1);
}

/* 快速搜索 */
.quick-search {
    position: relative;
    min-width: 250px;
}

.quick-search .search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--theme-gray-500, #adb5bd);
    font-size: 14px;
}

.quick-search .form-control {
    padding-left: 36px;
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-gray-300, #dee2e6);
    border-radius: 20px;
    font-size: 13px;
}

/* 统计信息 */
.table-stats {
    font-size: 13px;
    color: var(--theme-gray-600, #6c757d);
    font-weight: 500;
}

/* ==================== 现代表格设计 ==================== */
.enterprise-table-v2 {
    width: 100%;
    margin-bottom: 0;
    font-size: 13px;
    color: var(--theme-gray-800, #343a40);
    background-color: var(--theme-surface, #ffffff);
    border-collapse: separate;
    border-spacing: 0;
}

/* 表格头部 - 现代化设计 */
.enterprise-table-v2 thead th {
    background: linear-gradient(135deg, var(--theme-gray-100, #f8f9fa) 0%, var(--theme-gray-200, #e9ecef) 100%);
    color: var(--theme-gray-700, #495057);
    font-weight: 600;
    font-size: 13px;
    padding: 16px;
    border-bottom: 2px solid var(--theme-gray-300, #dee2e6);
    border-right: 1px solid var(--theme-gray-300, #dee2e6);
    text-align: left;
    vertical-align: middle;
    white-space: nowrap;
    position: relative;
    transition: background-color 0.15s ease;
}

.enterprise-table-v2 thead th:last-child {
    border-right: none;
}

.enterprise-table-v2 thead th:first-child {
    border-top-left-radius: 0;
}

.enterprise-table-v2 thead th:last-child {
    border-top-right-radius: 0;
}

/* 排序功能 */
.enterprise-table-v2 thead th.sortable {
    cursor: pointer;
    user-select: none;
    transition: all 0.15s ease;
}

.enterprise-table-v2 thead th.sortable:hover {
    background: linear-gradient(135deg, var(--theme-gray-200, #e9ecef) 0%, var(--theme-gray-300, #dee2e6) 100%);
}

.enterprise-table-v2 thead th.sortable::after {
    content: '\f0dc';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.3;
    font-size: 11px;
    transition: all 0.15s ease;
}

.enterprise-table-v2 thead th.sort-asc::after {
    content: '\f0de';
    opacity: 1;
    color: var(--theme-primary);
}

.enterprise-table-v2 thead th.sort-desc::after {
    content: '\f0dd';
    opacity: 1;
    color: var(--theme-primary);
}

/* 表格主体 - 现代化行设计 */
.enterprise-table-v2 tbody tr {
    transition: all 0.15s ease;
    border-bottom: 1px solid var(--theme-gray-200, #e9ecef);
}

.enterprise-table-v2 tbody tr:hover {
    background: linear-gradient(135deg, rgba(var(--theme-primary-rgb), 0.04) 0%, rgba(var(--theme-primary-rgb), 0.02) 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.enterprise-table-v2 tbody tr:nth-child(even) {
    background-color: var(--theme-gray-50, #f8f9fa);
}

.enterprise-table-v2 tbody tr:nth-child(even):hover {
    background: linear-gradient(135deg, rgba(var(--theme-primary-rgb), 0.06) 0%, rgba(var(--theme-primary-rgb), 0.04) 100%);
}

.enterprise-table-v2 tbody td {
    padding: 16px;
    vertical-align: middle;
    border-right: 1px solid var(--theme-gray-200, #e9ecef);
    font-size: 13px;
    color: var(--theme-gray-800, #343a40);
    line-height: 1.4;
}

.enterprise-table-v2 tbody td:last-child {
    border-right: none;
}

/* ==================== 特殊列样式 ==================== */
.number-col {
    text-align: right;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
    font-weight: 500;
    width: 80px;
}

.status-col {
    text-align: center;
    width: 100px;
}

.action-col {
    text-align: center;
    width: 120px;
}

.image-col {
    text-align: center;
    width: 80px;
}

/* 表格图片 */
.table-img {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid var(--theme-gray-200, #e9ecef);
    transition: all 0.15s ease;
}

.table-img:hover {
    transform: scale(1.1);
    border-color: var(--theme-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* ==================== 现代状态徽章 ==================== */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.15s ease;
}

.status-badge::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
}

.status-success {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-error {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-info {
    background: rgba(59, 130, 246, 0.1);
    color: #2563eb;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.status-secondary {
    background: rgba(107, 114, 128, 0.1);
    color: #4b5563;
    border: 1px solid rgba(107, 114, 128, 0.2);
}

/* ==================== 现代操作按钮 ==================== */
.action-buttons {
    display: flex;
    gap: 4px;
    justify-content: center;
}

.action-buttons .btn {
    padding: 6px 8px;
    font-size: 12px;
    border-radius: 6px;
    transition: all 0.15s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* ==================== 空状态设计 ==================== */
.table-empty {
    text-align: center;
    padding: 64px 24px;
    color: var(--theme-gray-600, #6c757d);
    background: linear-gradient(135deg, var(--theme-gray-50, #f8f9fa) 0%, var(--theme-gray-100, #f1f3f4) 100%);
}

.table-empty-icon {
    font-size: 48px;
    color: var(--theme-gray-400, #ced4da);
    margin-bottom: 16px;
}

.table-empty-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--theme-gray-700, #495057);
}

.table-empty-subtext {
    font-size: 14px;
    color: var(--theme-gray-600, #6c757d);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
    .enterprise-table-v2-container {
        border-radius: 8px;
        margin-bottom: 16px;
    }
    
    .smart-toolbar {
        flex-direction: column;
        gap: 12px;
        padding: 16px;
    }
    
    .toolbar-left,
    .toolbar-right {
        width: 100%;
        justify-content: space-between;
    }
    
    .toolbar-search-form {
        flex-direction: column;
        gap: 8px;
        max-width: none;
    }
    
    .quick-search {
        min-width: auto;
        width: 100%;
    }
    
    .enterprise-table-v2 {
        font-size: 12px;
    }
    
    .enterprise-table-v2 thead th,
    .enterprise-table-v2 tbody td {
        padding: 12px 8px;
    }
}
