/*
 * StudentsCMSSP 企业级表格系统 V3
 * 基于最佳实践的现代化设计，与主题系统完美集成
 * 版本: 3.0.0 - 专业企业版
 *
 * 设计原则：
 * 1. 布局优化：合理的列宽、间距和对齐
 * 2. 颜色搭配：统一的企业级颜色体系
 * 3. 整体效果：专业的视觉层次和用户体验
 */

/* ==================== 现代表格容器 - 基于最佳实践 ==================== */
.enterprise-table-v2-container {
    background: var(--theme-surface, #ffffff);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    margin: 24px 0;
    border: 1px solid var(--theme-gray-200, #e9ecef);
    transition: all 0.3s ease;
    position: relative;
}

.enterprise-table-v2-container:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

/* ==================== 智能工具栏 - 专业企业级设计 ==================== */
.smart-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid var(--theme-gray-300, #dee2e6);
    margin: 0;
    border-radius: 12px 12px 0 0;
    flex-wrap: wrap;
    gap: 16px;
    position: relative;
}

.smart-toolbar::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, var(--theme-primary, #007bff) 50%, transparent 100%);
    opacity: 0.3;
}

.toolbar-left {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
}

.toolbar-left h5 {
    font-size: 16px;
    font-weight: 600;
    color: var(--theme-gray-800, #343a40);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.toolbar-left p {
    font-size: 13px;
    color: var(--theme-gray-600, #6c757d);
    margin: 0;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.view-toggle .btn-group {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.action-buttons .btn-group {
    gap: 4px;
}

/* 搜索表单集成 */
.toolbar-search-form {
    display: flex;
    align-items: end;
    gap: 12px;
    flex: 1;
    max-width: 800px;
}

.toolbar-search-form .form-group {
    margin-bottom: 0;
}

.toolbar-search-form .form-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--theme-gray-600, #6c757d);
    margin-bottom: 4px;
}

.toolbar-search-form .form-control,
.toolbar-search-form .form-select {
    font-size: 13px;
    padding: 6px 12px;
    border-radius: 6px;
    border: 1px solid var(--theme-gray-300, #dee2e6);
    transition: all 0.15s ease;
}

.toolbar-search-form .form-control:focus,
.toolbar-search-form .form-select:focus {
    border-color: var(--theme-primary);
    box-shadow: 0 0 0 3px rgba(var(--theme-primary-rgb), 0.1);
}

/* 快速搜索 */
.quick-search {
    position: relative;
    min-width: 250px;
}

.quick-search .search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--theme-gray-500, #adb5bd);
    font-size: 14px;
}

.quick-search .form-control {
    padding-left: 36px;
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-gray-300, #dee2e6);
    border-radius: 20px;
    font-size: 13px;
}

/* 统计信息 */
.table-stats {
    font-size: 13px;
    color: var(--theme-gray-600, #6c757d);
    font-weight: 500;
}

/* ==================== 现代表格设计 - 基于最佳实践 ==================== */
.enterprise-table-v2 {
    width: 100%;
    margin-bottom: 0;
    font-size: 13px;
    color: var(--theme-gray-800, #343a40);
    background-color: var(--theme-surface, #ffffff);
    border-collapse: separate;
    border-spacing: 0;
    border: none;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.4;
}

/* 表格头部 - 企业级专业设计 */
.enterprise-table-v2 thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: var(--theme-gray-700, #495057);
    font-weight: 600;
    font-size: 13px;
    padding: 18px 16px;
    border-bottom: 2px solid var(--theme-gray-300, #dee2e6);
    border-right: 1px solid var(--theme-gray-200, #e9ecef);
    text-align: left;
    vertical-align: middle;
    white-space: nowrap;
    position: relative;
    transition: all 0.2s ease;
    text-transform: none;
    letter-spacing: 0.025em;
}

/* 统一表头背景色 - 避免列基础颜色 */
.enterprise-table-v2 thead th:first-child,
.enterprise-table-v2 thead th:last-child {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.enterprise-table-v2 thead th:last-child {
    border-right: none;
}

.enterprise-table-v2 thead th:first-child {
    border-top-left-radius: 0;
}

.enterprise-table-v2 thead th:last-child {
    border-top-right-radius: 0;
}

/* 排序功能 */
.enterprise-table-v2 thead th.sortable {
    cursor: pointer;
    user-select: none;
    transition: all 0.15s ease;
}

.enterprise-table-v2 thead th.sortable:hover {
    background: linear-gradient(135deg, var(--theme-gray-200, #e9ecef) 0%, var(--theme-gray-300, #dee2e6) 100%);
}

.enterprise-table-v2 thead th.sortable::after {
    content: '\f0dc';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.3;
    font-size: 11px;
    transition: all 0.15s ease;
}

.enterprise-table-v2 thead th.sort-asc::after {
    content: '\f0de';
    opacity: 1;
    color: var(--theme-primary);
}

.enterprise-table-v2 thead th.sort-desc::after {
    content: '\f0dd';
    opacity: 1;
    color: var(--theme-primary);
}

/* 表格主体 - 基于最佳实践的行设计 */
.enterprise-table-v2 tbody tr {
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--theme-gray-200, #e9ecef);
    background-color: #ffffff;
}

.enterprise-table-v2 tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* 移除斑马纹，使用简洁的线条分隔 */
.enterprise-table-v2 tbody tr:last-child {
    border-bottom: none;
}

.enterprise-table-v2 tbody td {
    padding: 16px;
    vertical-align: middle;
    border-right: 1px solid var(--theme-gray-200, #e9ecef);
    font-size: 13px;
    color: var(--theme-gray-800, #343a40);
    line-height: 1.4;
}

.enterprise-table-v2 tbody td:last-child {
    border-right: none;
}

/* ==================== 基于最佳实践的列对齐 ==================== */
/* 文本列左对齐 */
.enterprise-table-v2 th:nth-child(1),
.enterprise-table-v2 td:nth-child(1),
.enterprise-table-v2 th:nth-child(2),
.enterprise-table-v2 td:nth-child(2),
.enterprise-table-v2 th:nth-child(3),
.enterprise-table-v2 td:nth-child(3),
.enterprise-table-v2 th:nth-child(4),
.enterprise-table-v2 td:nth-child(4) {
    text-align: left;
}

/* 数字列右对齐 */
.enterprise-table-v2 th:nth-child(5),
.enterprise-table-v2 td:nth-child(5) {
    text-align: right;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
    font-weight: 500;
}

/* 单位列左对齐 */
.enterprise-table-v2 th:nth-child(6),
.enterprise-table-v2 td:nth-child(6) {
    text-align: left;
}

/* 日期列左对齐 */
.enterprise-table-v2 th:nth-child(7),
.enterprise-table-v2 td:nth-child(7),
.enterprise-table-v2 th:nth-child(8),
.enterprise-table-v2 td:nth-child(8) {
    text-align: left;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
}

/* 状态列居中 */
.enterprise-table-v2 th:nth-child(9),
.enterprise-table-v2 td:nth-child(9) {
    text-align: center;
}

/* 操作列居中 */
.enterprise-table-v2 th:nth-child(10),
.enterprise-table-v2 td:nth-child(10) {
    text-align: center;
}

/* ==================== 特殊列样式 ==================== */
.number-col {
    text-align: right;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
    font-weight: 500;
    width: 80px;
}

.status-col {
    text-align: center;
    width: 100px;
}

.action-col {
    text-align: center;
    width: 120px;
}

.image-col {
    text-align: center;
    width: 80px;
}

/* 表格图片 */
.table-img {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid var(--theme-gray-200, #e9ecef);
    transition: all 0.15s ease;
}

.table-img:hover {
    transform: scale(1.1);
    border-color: var(--theme-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* ==================== 专业状态徽章 - 企业级设计 ==================== */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    text-transform: none;
    letter-spacing: 0.025em;
}

.status-badge::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
    flex-shrink: 0;
}

.status-success {
    background: rgba(16, 185, 129, 0.12);
    color: #047857;
    border-color: rgba(16, 185, 129, 0.25);
}

.status-warning {
    background: rgba(245, 158, 11, 0.12);
    color: #b45309;
    border-color: rgba(245, 158, 11, 0.25);
}

.status-error {
    background: rgba(239, 68, 68, 0.12);
    color: #b91c1c;
    border-color: rgba(239, 68, 68, 0.25);
}

.status-info {
    background: rgba(59, 130, 246, 0.12);
    color: #1d4ed8;
    border-color: rgba(59, 130, 246, 0.25);
}

.status-secondary {
    background: rgba(107, 114, 128, 0.12);
    color: #374151;
    border-color: rgba(107, 114, 128, 0.25);
}

/* ==================== 现代操作按钮 ==================== */
.action-buttons {
    display: flex;
    gap: 4px;
    justify-content: center;
}

.action-buttons .btn {
    padding: 6px 8px;
    font-size: 12px;
    border-radius: 6px;
    transition: all 0.15s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* ==================== 空状态设计 ==================== */
.table-empty {
    text-align: center;
    padding: 64px 24px;
    color: var(--theme-gray-600, #6c757d);
    background: linear-gradient(135deg, var(--theme-gray-50, #f8f9fa) 0%, var(--theme-gray-100, #f1f3f4) 100%);
}

.table-empty-icon {
    font-size: 48px;
    color: var(--theme-gray-400, #ced4da);
    margin-bottom: 16px;
}

.table-empty-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--theme-gray-700, #495057);
}

.table-empty-subtext {
    font-size: 14px;
    color: var(--theme-gray-600, #6c757d);
}

/* ==================== 智能搜索表单 ==================== */
.smart-search-form {
    background: var(--theme-gray-50, #f8f9fa);
    border: 1px solid var(--theme-gray-200, #e9ecef);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
}

.search-row {
    display: flex;
    align-items: end;
    gap: 12px;
    flex-wrap: wrap;
}

.search-field {
    display: flex;
    flex-direction: column;
    min-width: 120px;
}

.search-field label {
    font-size: 12px;
    font-weight: 500;
    color: var(--theme-gray-600, #6c757d);
    margin-bottom: 4px;
}

.search-field .form-control,
.search-field .form-select {
    font-size: 13px;
    padding: 6px 12px;
    border-radius: 6px;
    border: 1px solid var(--theme-gray-300, #dee2e6);
    transition: all 0.15s ease;
}

.search-field .form-control:focus,
.search-field .form-select:focus {
    border-color: var(--theme-primary);
    box-shadow: 0 0 0 3px rgba(var(--theme-primary-rgb), 0.1);
}

.search-actions {
    display: flex;
    gap: 8px;
    align-items: end;
}

.search-actions .btn {
    padding: 6px 16px;
    font-size: 13px;
    border-radius: 6px;
}

/* ==================== 批量操作工具栏 ==================== */
.batch-operations-toolbar {
    background: linear-gradient(135deg, var(--theme-gray-50, #f8f9fa) 0%, var(--theme-gray-100, #f1f3f4) 100%);
    border: 1px solid var(--theme-gray-200, #e9ecef);
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
}

.selection-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.selection-count {
    font-size: 13px;
    color: var(--theme-gray-600, #6c757d);
    font-weight: 500;
}

.batch-actions .btn-group .btn {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
    .enterprise-table-v2-container {
        border-radius: 8px;
        margin-bottom: 16px;
    }
    
    .smart-toolbar {
        flex-direction: column;
        gap: 12px;
        padding: 16px;
    }
    
    .toolbar-left,
    .toolbar-right {
        width: 100%;
        justify-content: space-between;
    }
    
    .toolbar-search-form {
        flex-direction: column;
        gap: 8px;
        max-width: none;
    }
    
    .quick-search {
        min-width: auto;
        width: 100%;
    }
    
    .enterprise-table-v2 {
        font-size: 12px;
    }
    
    .enterprise-table-v2 thead th,
    .enterprise-table-v2 tbody td {
        padding: 12px 8px;
    }
}
