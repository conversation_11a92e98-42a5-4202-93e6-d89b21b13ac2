/* 
 * StudentsCMSSP 布局间隙修复
 * 解决左侧导航栏和右侧内容区域之间的白色间隙问题
 * 版本: 1.0.0
 */

/* ==================== 布局容器修复 ==================== */
.layout-container {
    display: flex !important;
    height: 100vh !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    gap: 0 !important; /* 确保没有间隙 */
}

/* ==================== 左侧导航栏修复 ==================== */
.sidebar {
    width: 200px !important;
    min-width: 200px !important;
    max-width: 200px !important;
    background: var(--theme-primary) !important;
    color: white !important;
    display: flex !important;
    flex-direction: column !important;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1) !important;
    transition: all 0.3s ease !important;
    z-index: 1000 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    flex-shrink: 0 !important; /* 防止收缩 */
}

/* ==================== 右侧内容区域修复 ==================== */
.main-content {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
    background: var(--bs-light, #f8f9fa) !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    min-width: 0 !important; /* 允许收缩 */
}

/* ==================== 内容区域修复 ==================== */
.content-area {
    flex: 1 !important;
    overflow-y: auto !important;
    padding: 1.5rem !important;
    background: var(--bs-body-bg, #ffffff) !important;
    margin: 0 !important;
    border: none !important;
}

/* ==================== 顶部工具栏修复 ==================== */
.top-toolbar {
    background: var(--theme-primary) !important;
    color: white !important;
    border-bottom: 1px solid rgba(255,255,255,0.1) !important;
    padding: 0.75rem 1.5rem !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05) !important;
    z-index: 999 !important;
    transition: background-color 0.3s ease !important;
    margin: 0 !important;
    border-left: none !important;
    border-right: none !important;
    border-top: none !important;
    flex-shrink: 0 !important;
}

/* ==================== 全局重置修复 ==================== */
/* 确保没有全局样式影响布局 */
body {
    margin: 0 !important;
    padding: 0 !important;
    height: 100vh !important;
    overflow: hidden !important;
}

html {
    margin: 0 !important;
    padding: 0 !important;
    height: 100% !important;
}

/* ==================== 容器修复 ==================== */
/* 确保Bootstrap容器不影响布局 */
.container,
.container-fluid {
    margin: 0 !important;
    padding: 0 !important;
    max-width: none !important;
    width: 100% !important;
}

/* ==================== 行和列修复 ==================== */
/* 确保Bootstrap行列不影响布局 */
.row {
    margin: 0 !important;
}

.col,
.col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
.col-auto,
.col-sm, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6,
.col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12,
.col-sm-auto,
.col-md, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6,
.col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12,
.col-md-auto,
.col-lg, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6,
.col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12,
.col-lg-auto,
.col-xl, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6,
.col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12,
.col-xl-auto {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

/* ==================== 响应式修复 ==================== */
@media (max-width: 768px) {
    .sidebar {
        position: fixed !important;
        left: -200px !important;
        height: 100vh !important;
        z-index: 1050 !important;
        transition: left 0.3s ease !important;
    }

    .sidebar.show {
        left: 0 !important;
    }

    .main-content {
        width: 100% !important;
        margin-left: 0 !important;
    }

    .mobile-toggle {
        display: block !important;
    }
}

@media (min-width: 769px) {
    .mobile-toggle {
        display: none !important;
    }
    
    .sidebar {
        position: relative !important;
        left: 0 !important;
    }
}

/* ==================== 调试辅助 ==================== */
/* 临时调试样式，可以在确认修复后删除 */
/*
.layout-container {
    border: 2px solid red !important;
}

.sidebar {
    border: 2px solid blue !important;
}

.main-content {
    border: 2px solid green !important;
}
*/

/* ==================== 特殊情况修复 ==================== */
/* 修复可能的第三方CSS冲突 */
.layout-container * {
    box-sizing: border-box !important;
}

/* 确保flexbox正常工作 */
.layout-container {
    align-items: stretch !important;
    justify-content: flex-start !important;
}

/* 防止内容溢出 */
.sidebar,
.main-content {
    overflow: hidden !important;
}

.sidebar-nav {
    overflow-y: auto !important;
}

.content-area {
    overflow-y: auto !important;
}

/* ==================== 最终确保 ==================== */
/* 最高优先级修复 */
body .layout-container {
    gap: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
}

body .layout-container .sidebar {
    margin-right: 0 !important;
    border-right: none !important;
}

body .layout-container .main-content {
    margin-left: 0 !important;
    border-left: none !important;
}
