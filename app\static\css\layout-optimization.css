/* 
 * StudentsCMSSP 布局优化样式
 * 解决底色与文字同色调问题，优化布局结构
 * 与主题系统完全兼容
 * 版本: 1.0.0
 */

/* ==================== 色彩对比度修正 ==================== */
/* 确保文字与背景有足够的对比度 */

/* 主要文字颜色修正 */
.text-primary {
    color: var(--theme-primary, #007bff) !important;
}

.text-secondary {
    color: var(--theme-secondary, #6c757d) !important;
}

.text-success {
    color: var(--theme-success, #28a745) !important;
}

.text-warning {
    color: #856404 !important; /* 加深警告色文字以提高对比度 */
}

.text-danger {
    color: var(--theme-danger, #dc3545) !important;
}

.text-info {
    color: var(--theme-info, #17a2b8) !important;
}

.text-muted {
    color: var(--theme-gray-600, #6c757d) !important;
}

.text-dark {
    color: var(--theme-gray-800, #343a40) !important;
}

/* 背景色与文字对比度修正 */
.bg-primary {
    background-color: var(--theme-primary, #007bff) !important;
    color: white !important;
}

.bg-primary .text-muted {
    color: rgba(255, 255, 255, 0.7) !important;
}

.bg-secondary {
    background-color: var(--theme-secondary, #6c757d) !important;
    color: white !important;
}

.bg-success {
    background-color: var(--theme-success, #28a745) !important;
    color: white !important;
}

.bg-warning {
    background-color: var(--theme-warning, #ffc107) !important;
    color: var(--theme-gray-800, #343a40) !important;
}

.bg-danger {
    background-color: var(--theme-danger, #dc3545) !important;
    color: white !important;
}

.bg-info {
    background-color: var(--theme-info, #17a2b8) !important;
    color: white !important;
}

.bg-light {
    background-color: var(--theme-gray-100, #f8f9fa) !important;
    color: var(--theme-gray-800, #343a40) !important;
}

.bg-dark {
    background-color: var(--theme-gray-800, #343a40) !important;
    color: white !important;
}

/* ==================== 表格工具栏布局优化 ==================== */
.table-toolbar {
    background: var(--theme-gray-50, #f8f9fa);
    padding: 12px 16px;
    border-bottom: 1px solid var(--theme-gray-200, #e9ecef);
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    flex-wrap: wrap;
    gap: 16px;
}

.table-toolbar-left {
    display: flex;
    align-items: flex-end;
    gap: 16px;
    flex: 1;
    min-width: 0;
}

.table-toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

/* 表格工具栏内的表单优化 */
.table-toolbar form {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    flex-wrap: wrap;
}

.table-toolbar .form-group {
    margin-bottom: 0;
    min-width: 0;
}

.table-toolbar .form-label {
    font-size: 12px !important;
    font-weight: 500 !important;
    color: var(--theme-gray-700, #495057) !important;
    margin-bottom: 4px !important;
    white-space: nowrap;
}

.table-toolbar .form-control,
.table-toolbar .form-select {
    font-size: 13px !important;
    padding: 6px 10px !important;
    height: 32px !important;
    border: 1px solid var(--theme-gray-300, #ced4da) !important;
    background-color: white !important;
    color: var(--theme-gray-800, #343a40) !important;
}

.table-toolbar .btn {
    height: 32px !important;
    padding: 6px 12px !important;
    font-size: 12px !important;
    line-height: 1.2 !important;
    white-space: nowrap;
}

/* ==================== 表格搜索框优化 ==================== */
.table-search {
    position: relative;
    min-width: 180px;
    max-width: 250px;
}

.table-search input {
    padding-left: 32px !important;
    font-size: 12px !important;
    height: 32px !important;
    background-color: white !important;
    border: 1px solid var(--theme-gray-300, #ced4da) !important;
    color: var(--theme-gray-800, #343a40) !important;
}

.table-search .search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--theme-gray-500, #adb5bd);
    font-size: 12px;
    z-index: 2;
}

/* ==================== 卡片头部布局优化 ==================== */
.card-header {
    padding: 16px 20px !important;
}

.card-header .card-title {
    font-size: 16px !important;
    font-weight: 600 !important;
    margin: 0 !important;
    line-height: 1.3 !important;
}

.card-header .card-tools {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.card-header .btn {
    font-size: 12px !important;
    padding: 6px 12px !important;
    height: 32px !important;
    line-height: 1.2 !important;
}

/* 移动端和桌面端布局切换优化 */
.desktop-only {
    display: block !important;
}

.mobile-only {
    display: none !important;
}

@media (max-width: 991.98px) {
    .desktop-only {
        display: none !important;
    }
    
    .mobile-only {
        display: block !important;
    }
    
    .table-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .table-toolbar-left,
    .table-toolbar-right {
        width: 100%;
        justify-content: space-between;
    }
    
    .table-toolbar form {
        flex-direction: column;
        gap: 8px;
    }
    
    .table-toolbar .form-group {
        width: 100%;
    }
    
    .table-toolbar .form-control,
    .table-toolbar .form-select {
        width: 100%;
    }
}

/* ==================== 按钮组布局优化 ==================== */
.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 12px;
}

.action-buttons .btn {
    flex: 1;
    min-width: 120px;
    text-align: center;
}

@media (max-width: 576px) {
    .action-buttons {
        flex-direction: column;
    }
    
    .action-buttons .btn {
        width: 100%;
        margin-bottom: 8px;
    }
}

/* ==================== 状态徽章优化 ==================== */
.status-badges {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
}

.status-badges .badge {
    font-size: 11px !important;
    padding: 3px 6px !important;
    line-height: 1.2 !important;
    white-space: nowrap;
}

/* ==================== 数据展示优化 ==================== */
.data-display {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.data-primary {
    font-weight: 500;
    color: var(--theme-gray-800, #343a40);
    font-size: 13px;
    line-height: 1.3;
}

.data-secondary {
    font-size: 11px;
    color: var(--theme-gray-600, #6c757d);
    line-height: 1.2;
}

/* ==================== 图标与文字对齐优化 ==================== */
.icon-text {
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.icon-text i {
    flex-shrink: 0;
    width: 14px;
    text-align: center;
}

/* ==================== 分页组件优化 ==================== */
.table-pagination {
    background: var(--theme-gray-50, #f8f9fa);
    padding: 12px 16px;
    border-top: 1px solid var(--theme-gray-200, #e9ecef);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
}

.pagination-info {
    font-size: 12px;
    color: var(--theme-gray-600, #6c757d);
    white-space: nowrap;
}

.pagination {
    margin: 0;
}

.pagination .page-link {
    font-size: 12px;
    padding: 6px 10px;
    color: var(--theme-primary, #007bff);
    border: 1px solid var(--theme-gray-300, #dee2e6);
    background-color: white;
    line-height: 1.2;
}

.pagination .page-link:hover {
    color: var(--theme-primary-dark, #0056b3);
    background-color: var(--theme-gray-100, #f8f9fa);
    border-color: var(--theme-gray-300, #dee2e6);
}

.pagination .page-item.active .page-link {
    background-color: var(--theme-primary, #007bff);
    border-color: var(--theme-primary, #007bff);
    color: white;
}

.pagination .page-item.disabled .page-link {
    color: var(--theme-gray-500, #adb5bd);
    background-color: var(--theme-gray-100, #f8f9fa);
    border-color: var(--theme-gray-300, #dee2e6);
}

/* ==================== 响应式优化 ==================== */
@media (max-width: 768px) {
    .table-pagination {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }
    
    .pagination-info {
        order: 2;
    }
    
    .pagination {
        order: 1;
    }
}

/* ==================== 无数据状态优化 ==================== */
.table-empty {
    text-align: center;
    padding: 48px 24px;
    color: var(--theme-gray-600, #6c757d);
    background-color: var(--theme-gray-50, #f8f9fa);
}

.table-empty-icon {
    font-size: 48px;
    color: var(--theme-gray-400, #ced4da);
    margin-bottom: 16px;
}

.table-empty-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--theme-gray-700, #495057);
}

.table-empty-subtext {
    font-size: 14px;
    color: var(--theme-gray-600, #6c757d);
}
