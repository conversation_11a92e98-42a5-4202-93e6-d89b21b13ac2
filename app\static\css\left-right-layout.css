/* 左右式布局 - 使用系统主题，确保无间隙 */

/* 布局容器 - 确保无间隙 */
.layout-container {
    display: flex !important;
    height: 100vh !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    gap: 0 !important;
}

/* 左侧导航栏 - 200px固定宽度，无间隙 */
.sidebar {
    width: 200px !important;
    min-width: 200px !important;
    max-width: 200px !important;
    background: var(--theme-primary);
    transition: background-color 0.3s ease;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    flex-shrink: 0 !important;
}

/* 右侧主内容区域 - 无间隙 */
.main-content {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    min-width: 0 !important;
}

.sidebar-header {
    background: var(--theme-primary);
    transition: background 0.3s ease;
}

.sidebar-nav .nav-link:hover {
    background: var(--theme-primary-dark);
}

.sidebar-nav .nav-link.active {
    background: var(--theme-primary-dark);
}

/* 右侧顶部工具栏 */
.top-toolbar {
    background: var(--theme-primary) !important;
    transition: background-color 0.3s ease;
    margin: 0 !important;
    padding: 0.75rem 1.5rem !important;
    border: none !important;
}

/* 内容区域 */
.content-area {
    flex: 1 !important;
    overflow-y: auto !important;
    padding: 1.5rem !important;
    margin: 0 !important;
    border: none !important;
}

/* 移动端响应式 */
@media (max-width: 768px) {
    .sidebar.show::before {
        content: '';
        position: fixed;
        top: 0;
        left: 200px;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        z-index: -1;
    }
}
