/* 极简版主题颜色系统 - 去除所有过度效果 */
:root {
  /* 默认主题颜色 */
  --theme-primary: #007bff;
  --theme-primary-light: #66b3ff;
  --theme-primary-dark: #0056b3;
  --theme-primary-rgb: 0, 123, 255;

  /* 语义化颜色 */
  --theme-success: #28a745;
  --theme-success-light: #5cbf60;
  --theme-success-dark: #1e7e34;
  --theme-info: #17a2b8;
  --theme-info-light: #4fc3d7;
  --theme-info-dark: #117a8b;
  --theme-warning: #ffc107;
  --theme-warning-light: #ffcd39;
  --theme-warning-dark: #e0a800;
  --theme-danger: #dc3545;
  --theme-danger-light: #e66370;
  --theme-danger-dark: #bd2130;
  --theme-secondary: #6c757d;
  --theme-secondary-light: #868e96;
  --theme-secondary-dark: #545b62;

  /* 中性色调 */
  --theme-gray-100: #f8f9fa;
  --theme-gray-200: #e9ecef;
  --theme-gray-300: #dee2e6;
  --theme-gray-400: #ced4da;
  --theme-gray-500: #adb5bd;
  --theme-gray-600: #6c757d;
  --theme-gray-700: #495057;
  --theme-gray-800: #343a40;
  --theme-gray-900: #212529;

  /* 背景色 */
  --theme-surface: #ffffff;
  --theme-surface-dark: #f8f9fa;
}

/* 主题配色方案 */

/* 海洋蓝主题 */
[data-theme="primary"] {
  --theme-primary: #007bff;
  --theme-primary-light: #66b3ff;
  --theme-primary-dark: #0056b3;
  --theme-primary-rgb: 0, 123, 255;
}

/* 现代灰主题 */
[data-theme="secondary"] {
  --theme-primary: #6c757d;
  --theme-primary-light: #868e96;
  --theme-primary-dark: #545b62;
  --theme-primary-rgb: 108, 117, 125;
}

/* 自然绿主题 */
[data-theme="success"] {
  --theme-primary: #28a745;
  --theme-primary-light: #5cbf60;
  --theme-primary-dark: #1e7e34;
  --theme-primary-rgb: 40, 167, 69;
}

/* 活力橙主题 */
[data-theme="warning"] {
  --theme-primary: #ffc107;
  --theme-primary-light: #ffcd39;
  --theme-primary-dark: #e0a800;
  --theme-primary-rgb: 255, 193, 7;
}

/* 优雅紫主题 */
[data-theme="info"] {
  --theme-primary: #17a2b8;
  --theme-primary-light: #4fc3d7;
  --theme-primary-dark: #117a8b;
  --theme-primary-rgb: 23, 162, 184;
}

/* 深邃红主题 */
[data-theme="danger"] {
  --theme-primary: #dc3545;
  --theme-primary-light: #e66370;
  --theme-primary-dark: #bd2130;
  --theme-primary-rgb: 220, 53, 69;
}

/* 基础组件样式 */

/* 导航栏样式 */
.navbar {
  background-color: var(--theme-primary) !important;
  border: none;
}

.navbar-brand {
  color: white !important;
  font-weight: 500;
}

.navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
}

.navbar-nav .nav-link:hover {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.1);
}

.navbar-nav .nav-link.active {
  background-color: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

/* 按钮样式 */
.btn-primary {
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--theme-primary-dark);
  border-color: var(--theme-primary-dark);
  color: white;
}

.btn-primary:focus,
.btn-primary.focus {
  outline: 2px solid rgba(var(--theme-primary-rgb), 0.5);
  outline-offset: 2px;
}

.btn-primary:active {
  background-color: var(--theme-primary-dark);
  border-color: var(--theme-primary-dark);
}

/* 卡片样式 */
.card {
  border: 1px solid var(--theme-gray-200);
  border-radius: 4px;
  background-color: var(--theme-surface);
}

.card-header {
  background-color: var(--theme-primary) !important;
  color: white !important;
  border-bottom: none !important;
  font-weight: 500;
}

.card-header h1,
.card-header h2,
.card-header h3,
.card-header h4,
.card-header h5,
.card-header h6 {
  color: white !important;
  margin: 0;
}

/* 表单控件样式 */
.form-control {
  border: 1px solid var(--theme-gray-300);
  border-radius: 4px;
  background-color: var(--theme-surface);
}

.form-control:focus {
  border-color: var(--theme-primary);
  outline: 2px solid rgba(var(--theme-primary-rgb), 0.25);
  outline-offset: -2px;
}

/* 徽章样式 */
.badge-primary {
  background-color: var(--theme-primary);
  color: white;
}

.badge-secondary {
  background-color: var(--theme-secondary);
  color: white;
}

.badge-success {
  background-color: var(--theme-success);
  color: white;
}

.badge-warning {
  background-color: var(--theme-warning);
  color: var(--theme-gray-800);
}

.badge-danger {
  background-color: var(--theme-danger);
  color: white;
}

.badge-info {
  background-color: var(--theme-info);
  color: white;
}

/* 链接样式 */
a {
  color: var(--theme-primary);
  text-decoration: none;
}

a:hover {
  color: var(--theme-primary-dark);
}

/* 表格样式 */
.table-primary {
  background-color: rgba(var(--theme-primary-rgb), 0.1);
}

.table-primary th,
.table-primary td {
  border-color: rgba(var(--theme-primary-rgb), 0.2);
}



/* 进度条样式 */
.progress-bar {
  background-color: var(--theme-primary);
}

/* 分页样式 */
.page-link {
  color: var(--theme-primary);
}

.page-link:hover {
  color: var(--theme-primary-dark);
  background-color: var(--theme-gray-100);
}

.page-item.active .page-link {
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
}

/* 下拉菜单样式 */
.dropdown-item:hover,
.dropdown-item:focus {
  background-color: var(--theme-gray-100);
}

.dropdown-item.active {
  background-color: var(--theme-primary);
  color: white;
}

/* 警告框样式 */
.alert-primary {
  color: var(--theme-primary-dark);
  background-color: #e3f2fd;
  border-color: #bbdefb;
}

.alert-success {
  color: var(--theme-success-dark);
  background-color: #e8f5e8;
  border-color: #c8e6c9;
}

.alert-warning {
  color: var(--theme-warning-dark);
  background-color: #fff8e1;
  border-color: #ffecb3;
}

.alert-danger {
  color: var(--theme-danger-dark);
  background-color: #ffebee;
  border-color: #ffcdd2;
}

.alert-info {
  color: var(--theme-info-dark);
  background-color: #e0f2f1;
  border-color: #b2dfdb;
}
