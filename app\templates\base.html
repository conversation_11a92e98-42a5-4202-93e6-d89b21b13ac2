<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="{{ theme_color|default('primary') }}">
    <title>{% block title %}{{ project_name|default('校园餐智慧食堂(Scmmp) ') }}{% endblock %}</title>

    <!-- 动态Favicon -->
    {% if system_logo %}
    <link rel="icon" type="image/x-icon" href="{{ system_logo }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ system_logo }}">
    <link rel="apple-touch-icon" href="{{ system_logo }}">
    {% else %}
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/favicon.ico') }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ url_for('static', filename='img/favicon.ico') }}">
    {% endif %}
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap/css/bootstrap.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/toastr/css/toastr.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/jquery-ui/css/jquery-ui.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/datatables/css/dataTables.bootstrap4.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap-table/bootstrap-table.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/select2/select2.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/local-fonts.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/theme-colors.css') }}?v=2.4.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/table-optimization.css') }}?v=2.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard-optimization.css') }}?v=1.5.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-image-uploader.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/file-upload-fix.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/inventory-optimization.css') }}?v=1.0.0">

    <!-- 自定义 CSP 修复样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/custom-csp-fixes.css') }}?v=1.0.0">
    <!-- 简化版主题颜色系统 - 必须在其他样式之前加载 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/theme-colors.css') }}?v=1.0.0">
    <!-- 优雅导航样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/elegant-navigation.css') }}?v=1.0.0">
    <!-- 左右式布局样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/left-right-layout.css') }}?v=1.0.0">

    <!-- 移动端优化样式 - 最后加载确保优先级 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile-optimization.css') }}?v=1.1.0">

    <!-- 企业级桌面端UI样式 - 与主题系统集成 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enterprise-desktop-ui.css') }}?v=2.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enterprise-table-enhanced.css') }}?v=2.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enterprise-dashboard.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enterprise-forms.css') }}?v=2.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enterprise-buttons.css') }}?v=2.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enterprise-navigation.css') }}?v=1.0.0">

    <!-- 企业级修正样式 - 最后加载确保优先级 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enterprise-table-fixes.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/layout-optimization.css') }}?v=1.0.0">

    {% block styles %}{% endblock %}
    <style>
        /* 左右式布局样式 - 全局重置 */
        html, body {
            margin: 0 !important;
            padding: 0 !important;
            height: 100% !important;
            overflow: hidden;
            box-sizing: border-box;
        }

        *, *::before, *::after {
            box-sizing: border-box;
        }

        /* 确保Bootstrap容器不影响布局 */
        .container, .container-fluid {
            margin: 0 !important;
            padding: 0 !important;
            max-width: none !important;
            width: 100% !important;
        }

        .row {
            margin: 0 !important;
        }

        [class*="col-"] {
            padding-left: 0 !important;
            padding-right: 0 !important;
        }

        .layout-container {
            display: flex;
            height: 100vh;
            width: 100%;
            margin: 0;
            padding: 0;
            gap: 0;
        }

        /* 左侧导航栏 */
        .sidebar {
            width: 200px;
            min-width: 200px;
            max-width: 200px;
            background: var(--theme-primary);
            color: white;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            z-index: 1000;
            margin: 0;
            padding: 0;
            border: none;
            flex-shrink: 0;
        }

        .sidebar-header {
            padding: 1rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: var(--theme-primary);
        }

        .sidebar-brand {
            color: white;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .sidebar-brand:hover {
            color: white;
            text-decoration: none;
        }

        .sidebar-nav {
            flex: 1;
            overflow-y: auto;
            padding: 0.5rem 0;
        }

        .sidebar-nav .nav-item {
            margin: 0.2rem 0.5rem;
        }

        .sidebar-nav .nav-link {
            color: rgba(255,255,255,0.9);
            padding: 0.75rem 1rem;
            border-radius: 6px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .sidebar-nav .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            text-decoration: none;
        }

        .sidebar-nav .nav-link.active {
            background: rgba(255,255,255,0.2);
            color: white;
        }

        /* 右侧内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            background: var(--bs-light, #f8f9fa);
            margin: 0;
            padding: 0;
            border: none;
            min-width: 0;
        }

        /* 顶部工具栏 */
        .top-toolbar {
            background: var(--theme-primary);
            color: white;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            padding: 0.75rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            z-index: 999;
            transition: background-color 0.3s ease;
        }

        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .toolbar-right {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* 内容区域 */
        .content-area {
            flex: 1;
            overflow-y: auto;
            padding: 1.5rem;
            background: var(--bs-body-bg, #ffffff);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -200px;
                height: 100vh;
                z-index: 1050;
                transition: left 0.3s ease;
            }

            .sidebar.show {
                left: 0;
            }

            .main-content {
                width: 100%;
            }

            .mobile-toggle {
                display: block !important;
            }
        }

        @media (min-width: 769px) {
            .mobile-toggle {
                display: none !important;
            }
        }

        /* 左右式布局特定样式 - 使用现有主题系统 */
        /* 主题变量已在 theme-colors.css 中定义，这里只做布局相关的补充 */

        .navbar-school-name {
            font-size: 0.95rem;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.95);
            white-space: nowrap;
            margin-top: 6px;
            letter-spacing: 0.3px;
            padding: 2px 8px;
            background-color: rgba(255, 255, 255, 0.15);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .navbar-logo {
                height: 32px !important;
                max-width: 100px !important;
            }
            .navbar-brand-text {
                font-size: 0.9rem;
            }
            .navbar-school-name {
                font-size: 0.75rem;
            }
        }

        /* 超小屏幕只显示LOGO */
        @media (max-width: 480px) {
            .navbar-brand-text {
                display: none;
            }
            .navbar-school-name {
                display: none;
            }
            .navbar-logo {
                margin-right: 0 !important;
            }
        }
    </style>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/critical-handler-simple.js') }}"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/comprehensive-event-handler.js') }}"></script>
</head>
<body data-theme="{{ theme_color|default('primary') }}">
    <div class="layout-container">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <!-- 侧边栏头部 -->
            <div class="sidebar-header">
                <a class="sidebar-brand" href="{{ url_for('main.index') }}">
                    {% if system_logo %}
                    <img src="{{ system_logo }}" alt="{{ project_name }}" style="height: 24px; width: auto;">
                    {% endif %}
                    <div>
                        <div style="font-size: 0.95rem; font-weight: 600;">{{ project_name|default('智慧食堂平台') }}</div>
                        {% if current_user.is_authenticated and current_user.get_current_area() %}
                        <div style="font-size: 0.75rem; opacity: 0.8;">{{ current_user.get_current_area().name }}</div>
                        {% endif %}
                    </div>
                </a>
            </div>

            <!-- 侧边栏导航 -->
            <div class="sidebar-nav">
                {% if current_user.is_authenticated %}
                    {% for menu_item in user_menu %}
                        {% if menu_item.children %}
                            <!-- 有子菜单的项目 -->
                            <div class="nav-item">
                                <a class="nav-link" href="#" data-toggle="collapse" data-target="#{{ menu_item.id }}Submenu" aria-expanded="false">
                                    {% if menu_item.icon %}<i class="{{ menu_item.icon }}"></i>{% endif %}
                                    {{ menu_item.name }}
                                    <i class="fas fa-chevron-down ms-auto" style="font-size: 0.8rem;"></i>
                                </a>
                                <div class="collapse" id="{{ menu_item.id }}Submenu">
                                    <div style="padding-left: 1rem;">
                                        {% for child in menu_item.children %}
                                            {% if child.get('is_header') %}
                                                <div style="padding: 0.5rem 1rem; font-size: 0.8rem; opacity: 0.7; font-weight: 600;">{{ child.name }}</div>
                                            {% else %}
                                                <a class="nav-link" href="{{ url_for(child.url) if not child.get('url_params') else get_url(child) }}" style="padding: 0.5rem 1rem; font-size: 0.85rem;">
                                                    {% if child.icon %}<i class="{{ child.icon }}"></i>{% endif %}
                                                    {{ child.name }}
                                                </a>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        {% else %}
                            <!-- 普通菜单项 -->
                            <div class="nav-item">
                                <a class="nav-link" href="{{ url_for(menu_item.url) if not menu_item.get('url_params') else get_url(menu_item) }}">
                                    {% if menu_item.icon %}<i class="{{ menu_item.icon }}"></i>{% endif %}
                                    {{ menu_item.name }}
                                </a>
                            </div>
                        {% endif %}
                    {% endfor %}
                {% endif %}
            </div>
        </div>

        <!-- 右侧主内容区域 -->
        <div class="main-content">
            <!-- 顶部工具栏 -->
            <div class="top-toolbar">
                <div class="toolbar-left">
                    <!-- 移动端菜单切换按钮 -->
                    <button class="btn btn-outline-secondary mobile-toggle" type="button" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>

                <div class="toolbar-right">
                    <!-- 主题切换器 -->
                    <div class="dropdown">
                        <a class="btn btn-outline-light" href="#" id="themeDropdown" role="button" data-toggle="dropdown" title="切换主题">
                            <i class="fas fa-palette"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right theme-switcher-panel" style="min-width: 200px;">
                            <h6 class="dropdown-header">主题选择</h6>
                            <a class="dropdown-item theme-option" href="#" data-theme="primary">
                                <span class="theme-preview primary"></span>海洋蓝
                            </a>
                            <a class="dropdown-item theme-option" href="#" data-theme="success">
                                <span class="theme-preview success"></span>自然绿
                            </a>
                            <a class="dropdown-item theme-option" href="#" data-theme="warning">
                                <span class="theme-preview warning"></span>活力橙
                            </a>
                            <a class="dropdown-item theme-option" href="#" data-theme="info">
                                <span class="theme-preview info"></span>优雅紫
                            </a>
                            <a class="dropdown-item theme-option" href="#" data-theme="danger">
                                <span class="theme-preview danger"></span>深邃红
                            </a>
                        </div>
                    </div>

                    {% if current_user.is_authenticated %}
                    <!-- 通知图标 -->
                    <div class="dropdown">
                        <a class="btn btn-outline-light" href="#" id="notificationDropdown" role="button" data-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            {% if current_user.unread_notifications_count > 0 %}
                            <span class="badge badge-danger notification-badge" style="position: absolute; top: -5px; right: -5px; font-size: 0.7rem;">{{ current_user.unread_notifications_count }}</span>
                            {% endif %}
                        </a>
                        <div class="dropdown-menu dropdown-menu-right notification-dropdown" style="min-width: 300px;">
                            <h6 class="dropdown-header">通知中心</h6>
                            <div id="notification-list" style="max-height: 300px; overflow-y: auto;">
                                {% if current_user.recent_notifications %}
                                    {% for notification in current_user.recent_notifications %}
                                    <a class="dropdown-item notification-item {% if not notification['is_read'] %}unread{% endif %}" href="{{ url_for('notification.view', id=notification['id']) }}">
                                        <div class="notification-title">
                                            {% if notification['level'] == 2 %}
                                            <span class="badge badge-danger">紧急</span>
                                            {% elif notification['level'] == 1 %}
                                            <span class="badge badge-warning">重要</span>
                                            {% endif %}
                                            {{ notification['title'] }}
                                        </div>
                                        <div class="notification-content">{{ notification['content']|truncate(50) }}</div>
                                        <div class="notification-time">
                                            {% if notification['created_at'] is string %}
                                                {{ notification['created_at'] }}
                                            {% else %}
                                                {{ notification['created_at']|format_datetime }}
                                            {% endif %}
                                        </div>
                                    </a>
                                    {% endfor %}
                                {% else %}
                                <div class="dropdown-item text-center">暂无通知</div>
                                {% endif %}
                            </div>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-center" href="{{ url_for('notification.index') }}">查看全部通知</a>
                            {% if current_user.unread_notifications_count > 0 %}
                            <a class="dropdown-item text-center" href="{{ url_for('notification.mark_all_read') }}">全部标为已读</a>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 用户菜单 -->
                    <div class="dropdown">
                        <a class="btn btn-outline-light" href="#" id="userDropdown" role="button" data-toggle="dropdown">
                            <i class="fas fa-user"></i>
                            <span>{{ current_user.username }}</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right">
                            <a class="dropdown-item" href="#">
                                <i class="fas fa-user"></i> 个人资料
                            </a>
                            <a class="dropdown-item" href="{{ url_for('help.index') }}">
                                <i class="fas fa-question-circle"></i> 帮助中心
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt"></i> 退出登录
                            </a>
                        </div>
                    </div>
                    {% else %}
                    <a class="btn btn-outline-primary" href="{{ url_for('auth.login') }}">登录</a>
                    <a class="btn btn-primary" href="{{ url_for('auth.register') }}">注册</a>
                    {% endif %}
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content-area">
                {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show">
                    {{ message }}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
                {% endfor %}
                {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </div>
        </div>
    </div>



    <!-- 首先加载 jQuery -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}?v=1.0.0"></script>

    <!-- 事件处理器管理器（必须在其他事件处理器之前加载） -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/event-handler-manager.js') }}"></script>

    <!-- 其他脚本 -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/csp-helper.js') }}"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='bootstrap/js/bootstrap.bundle.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/moment/moment.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/moment/moment-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='bootstrap/js/bootstrap-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery-ui/js/jquery-ui.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery-ui/jquery-ui-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/datepicker-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery-ui-touch-punch/jquery.ui.touch-punch.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/toastr/toastr.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/toastr/toastr-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/datatables/datatables-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/bootstrap-table/bootstrap-table.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/bootstrap-table/bootstrap-table-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/select2/select2.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/select2/select2-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/chart-js/chart-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/sweetalert2/sweetalert2-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/form-validation-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/i18n.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/mock-api-handler.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/main.js') }}?v=1.0.2"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/theme-switcher-simple.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/auth-helper.js') }}?v=1.0.1"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/enhanced-image-uploader.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/file-upload-fix.js') }}?v=1.0.2"></script>
    <!-- 移动端表格转卡片工具 -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/mobile-table-cards.js') }}?v=1.0.0"></script>
    <!-- 移动端增强功能 -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/mobile-enhancements.js') }}?v=2.0.0"></script>
    <!-- 左右式布局JavaScript -->
    <script nonce="{{ csp_nonce }}">
        // 移动端侧边栏切换
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }

        // 点击内容区域关闭侧边栏
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                const sidebar = document.querySelector('.sidebar');
                const mainContent = document.querySelector('.main-content');

                if (mainContent.contains(e.target) && !e.target.closest('.mobile-toggle')) {
                    sidebar.classList.remove('show');
                }
            }
        });

        // 窗口大小改变时的处理
        window.addEventListener('resize', function() {
            const sidebar = document.querySelector('.sidebar');
            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
            }
        });

        $(document).ready(function() {
            // 侧边栏子菜单切换
            $('.sidebar-nav [data-toggle="collapse"]').on('click', function(e) {
                e.preventDefault();
                const target = $(this).attr('data-target');
                const $target = $(target);
                const $icon = $(this).find('.fa-chevron-down');

                // 切换子菜单
                $target.collapse('toggle');

                // 旋转箭头图标
                $target.on('show.bs.collapse', function() {
                    $icon.css('transform', 'rotate(180deg)');
                });

                $target.on('hide.bs.collapse', function() {
                    $icon.css('transform', 'rotate(0deg)');
                });
            });

            // 高亮当前页面的导航项
            const currentPath = window.location.pathname;
            $('.sidebar-nav .nav-link').each(function() {
                const href = $(this).attr('href');
                if (href && currentPath.includes(href) && href !== '/') {
                    $(this).addClass('active');
                    // 如果是子菜单项，展开父菜单
                    const $collapse = $(this).closest('.collapse');
                    if ($collapse.length) {
                        $collapse.addClass('show');
                        const $parentLink = $collapse.prev().find('[data-toggle="collapse"]');
                        $parentLink.find('.fa-chevron-down').css('transform', 'rotate(180deg)');
                    }
                }
            });

            // 优化移动端触摸
            if ('ontouchstart' in window && window.innerWidth <= 768) {
                $('.btn, .nav-link, .dropdown-item').each(function() {
                    const $this = $(this);
                    if ($this.height() < 44) {
                        $this.css({
                            'min-height': '44px',
                            'display': 'flex',
                            'align-items': 'center'
                        });
                    }
                });
            }
        });
    </script>

    <!-- 临时递归修复脚本 -->
    <script nonce="{{ csp_nonce }}">
        // 检测到递归错误时自动修复
        window.addEventListener('error', function(e) {
            if (e.message && e.message.includes('Maximum call stack size exceeded')) {
                console.error('检测到递归错误，正在自动修复...');

                // 加载快速修复脚本
                const script = document.createElement('script');
                script.src = '/static/js/quick_fix_recursion.js?v=' + Date.now();
                script.onload = function() {
                    console.log('递归修复脚本已加载');
                };
                document.head.appendChild(script);
            }
        });
    </script>
    <script nonce="{{ csp_nonce }}">
        // 配置toastr通知
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };



        // 初始化本地化设置
        $(document).ready(function() {
            // 设置 moment.js 的语言
            moment.locale('zh-CN');

            // 设置 bootstrap-table 的默认选项
            $.extend($.fn.bootstrapTable.defaults, {
                locale: 'zh-CN',
                formatLoadingMessage: function() {
                    return '正在加载中...';
                }
            });
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
