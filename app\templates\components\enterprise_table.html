<!-- 企业级表格组件 - 现代化设计 -->

<!-- 高级企业表格宏 -->
{% macro enterprise_table_v2(
    title="数据列表",
    subtitle=none,
    headers=[],
    data=[],
    pagination=none,
    search_form=none,
    actions=true,
    export_enabled=true,
    import_enabled=false,
    refresh_enabled=true,
    quick_search=true,
    table_class="enterprise-table-v2",
    container_class="enterprise-table-v2-container"
) %}

<div class="{{ container_class }}">
    <!-- 智能工具栏 -->
    <div class="smart-toolbar">
        <div class="toolbar-left">
            {% if search_form %}
            <!-- 集成搜索表单 -->
            <form method="GET" class="toolbar-search-form">
                {{ search_form }}
                <button type="submit" class="btn btn-primary btn-sm">
                    <i class="fas fa-search"></i> 搜索
                </button>
                {% if request.args %}
                <a href="{{ request.endpoint }}" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-times"></i> 清除
                </a>
                {% endif %}
            </form>
            {% endif %}
            
            <!-- 统计信息 -->
            <div class="table-stats">
                {% if pagination %}
                共 {{ pagination.total }} 条记录
                {% else %}
                共 {{ data|length }} 条记录
                {% endif %}
            </div>
        </div>
        
        <div class="toolbar-right">
            {% if quick_search %}
            <!-- 快速搜索 -->
            <div class="quick-search">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="form-control" placeholder="快速筛选..." id="quickSearch">
            </div>
            {% endif %}
            
            <!-- 操作按钮 -->
            {% if export_enabled %}
            <button class="btn btn-outline-secondary btn-sm" onclick="exportTableData()">
                <i class="fas fa-download"></i> 导出
            </button>
            {% endif %}
            
            {% if import_enabled %}
            <button class="btn btn-outline-success btn-sm" onclick="importTableData()">
                <i class="fas fa-upload"></i> 导入
            </button>
            {% endif %}
            
            {% if refresh_enabled %}
            <button class="btn btn-outline-primary btn-sm" onclick="refreshTable()">
                <i class="fas fa-sync"></i> 刷新
            </button>
            {% endif %}
            
            <!-- 自定义工具栏按钮 -->
            {% if caller %}
            {{ caller() }}
            {% endif %}
        </div>
    </div>
    
    <!-- 表格主体 -->
    <table class="{{ table_class }}">
        <thead>
            <tr>
                {% for header in headers %}
                <th class="{{ header.class if header.class else '' }} {{ 'sortable' if header.sortable else '' }}"
                    {% if header.width %}style="width: {{ header.width }}"{% endif %}>
                    {{ header.title }}
                </th>
                {% endfor %}
            </tr>
        </thead>
        <tbody>
            {% if data %}
                {% for row in data %}
                <tr>
                    {% for cell in row %}
                    <td class="{{ cell.class if cell.class else '' }}">
                        {% if cell.type == 'image' %}
                        <img src="{{ cell.value }}" alt="{{ cell.alt or '' }}" class="table-img"
                             onerror="this.src='{{ url_for('static', filename='img/qr-code-placeholder.png') }}';">
                        {% elif cell.type == 'badge' %}
                        <span class="status-badge status-{{ cell.status or 'secondary' }}">
                            {{ cell.value }}
                        </span>
                        {% elif cell.type == 'actions' %}
                        <div class="action-buttons">
                            {% for action in cell.actions %}
                            {% if action.type == 'link' %}
                            <a href="{{ action.url }}" class="btn btn-outline-{{ action.color or 'primary' }} btn-sm"
                               title="{{ action.title or '' }}">
                                <i class="{{ action.icon }}"></i>
                            </a>
                            {% elif action.type == 'button' %}
                            <button class="btn btn-outline-{{ action.color or 'primary' }} btn-sm"
                                    onclick="{{ action.onclick }}" title="{{ action.title or '' }}">
                                <i class="{{ action.icon }}"></i>
                            </button>
                            {% endif %}
                            {% endfor %}
                        </div>
                        {% else %}
                        {{ cell.value }}
                        {% endif %}
                    </td>
                    {% endfor %}
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="{{ headers|length }}" class="table-empty">
                    <div class="table-empty-icon">
                        <i class="fas fa-inbox"></i>
                    </div>
                    <div class="table-empty-text">暂无数据</div>
                    <div class="table-empty-subtext">您可以添加新数据或调整筛选条件</div>
                </td>
            </tr>
            {% endif %}
        </tbody>
    </table>
    
    <!-- 分页 -->
    {% if pagination and pagination.pages > 1 %}
    <div class="table-pagination">
        <div class="pagination-info">
            显示第 {{ (pagination.page - 1) * pagination.per_page + 1 }} -
            {{ (pagination.page * pagination.per_page) if (pagination.page * pagination.per_page < pagination.total) else pagination.total }}
            条，共 {{ pagination.total }} 条记录
        </div>
        <nav>
            <ul class="pagination pagination-sm">
                {% if pagination.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for(request.endpoint, page=pagination.prev_num, **request.args) }}">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                </li>
                {% endif %}

                {% for page_num in pagination.iter_pages() %}
                    {% if page_num %}
                        {% if page_num == pagination.page %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% else %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for(request.endpoint, page=page_num, **request.args) }}">{{ page_num }}</a>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if pagination.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for(request.endpoint, page=pagination.next_num, **request.args) }}">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}
</div>

<!-- 表格增强脚本 -->
<script nonce="{{ csp_nonce }}">
document.addEventListener('DOMContentLoaded', function() {
    // 快速搜索功能
    const quickSearch = document.getElementById('quickSearch');
    if (quickSearch) {
        quickSearch.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('.{{ table_class }} tbody tr');
            let visibleCount = 0;
            
            tableRows.forEach(function(row) {
                const rowText = row.textContent.toLowerCase();
                if (rowText.includes(searchTerm)) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });
            
            // 更新统计信息
            const statsElement = document.querySelector('.table-stats');
            if (statsElement) {
                statsElement.textContent = `显示 ${visibleCount} 条记录`;
            }
        });
    }
    
    // 表格排序功能
    document.querySelectorAll('.{{ table_class }} th.sortable').forEach(function(header) {
        header.addEventListener('click', function() {
            const table = this.closest('table');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const columnIndex = Array.from(this.parentNode.children).indexOf(this);
            
            // 移除其他列的排序状态
            this.parentNode.querySelectorAll('th').forEach(function(th) {
                if (th !== header) {
                    th.classList.remove('sort-asc', 'sort-desc');
                }
            });
            
            // 切换排序状态
            const isAsc = this.classList.contains('sort-asc');
            this.classList.remove('sort-asc', 'sort-desc');
            this.classList.add(isAsc ? 'sort-desc' : 'sort-asc');
            
            // 排序行
            rows.sort(function(a, b) {
                const aText = a.children[columnIndex].textContent.trim();
                const bText = b.children[columnIndex].textContent.trim();
                
                // 数字排序
                if (!isNaN(aText) && !isNaN(bText)) {
                    return isAsc ? bText - aText : aText - bText;
                }
                
                // 文本排序
                return isAsc ? bText.localeCompare(aText) : aText.localeCompare(bText);
            });
            
            // 重新排列行
            rows.forEach(function(row) {
                tbody.appendChild(row);
            });
        });
    });
    
    // 表格行悬停效果
    document.querySelectorAll('.{{ table_class }} tbody tr').forEach(function(row) {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-1px)';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });
});

// 全局工具函数
window.exportTableData = function() {
    console.log('导出功能开发中...');
    // 这里可以添加具体的导出逻辑
};

window.importTableData = function() {
    console.log('导入功能开发中...');
    // 这里可以添加具体的导入逻辑
};

window.refreshTable = function() {
    window.location.reload();
};
</script>

{% endmacro %}

<!-- 简化版表格宏 -->
{% macro simple_table(headers, data, actions=true) %}
{{ enterprise_table_v2(
    headers=headers,
    data=data,
    actions=actions,
    search_form=none,
    quick_search=false,
    export_enabled=false,
    import_enabled=false
) }}
{% endmacro %}
