{% extends "base.html" %}

{% block title %}食材溯源 - 二维码扫描{% endblock %}

{% block content %}
<div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-qrcode"></i> 食材溯源 - 二维码扫描
                    </h3>
                </div>
                <div class="card-body">
                    <!-- 扫描方式选择 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-camera"></i> 摄像头扫描</h5>
                                </div>
                                <div class="card-body text-center">
                                    <div id="qr-reader" style="width: 100%; max-width: 400px; margin: 0 auto;"></div>
                                    <button id="start-scan" class="btn btn-primary mt-3">
                                        <i class="fas fa-camera"></i> 开始扫描
                                    </button>
                                    <button id="stop-scan" class="btn btn-secondary mt-3" style="display: none;">
                                        <i class="fas fa-stop"></i> 停止扫描
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-upload"></i> 上传图片扫描</h5>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label for="qr-file">选择包含二维码的图片：</label>
                                        <input type="file" class="form-control-file" id="qr-file" accept="image/*">
                                    </div>
                                    <button id="scan-file" class="btn btn-success">
                                        <i class="fas fa-search"></i> 扫描图片
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 手动输入 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-keyboard"></i> 手动输入溯源信息</h5>
                                </div>
                                <div class="card-body">
                                    <form id="manual-trace-form">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="batch-number">批次号：</label>
                                                    <input type="text" class="form-control" id="batch-number" placeholder="请输入批次号">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="ingredient-name">食材名称：</label>
                                                    <input type="text" class="form-control" id="ingredient-name" placeholder="请输入食材名称">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label>&nbsp;</label>
                                                    <button type="submit" class="btn btn-primary btn-block">
                                                        <i class="fas fa-search"></i> 查询溯源
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 扫描结果显示 -->
                    <div id="scan-result" style="display: none;">
                        <div class="alert alert-success">
                            <h5><i class="fas fa-check-circle"></i> 扫描成功</h5>
                            <p>已识别到食材溯源信息，正在查询详细数据...</p>
                        </div>
                    </div>

                    <!-- 溯源结果显示 -->
                    <div id="trace-result" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-route"></i> 溯源结果</h5>
                            </div>
                            <div class="card-body" id="trace-content">
                                <!-- 溯源内容将通过JavaScript动态加载 -->
                            </div>
                        </div>
                    </div>

                    <!-- 错误信息显示 -->
                    <div id="error-message" style="display: none;">
                        <div class="alert alert-danger">
                            <h5><i class="fas fa-exclamation-triangle"></i> 扫描失败</h5>
                            <p id="error-text"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<!-- 引入二维码扫描库 -->
<script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    let html5QrcodeScanner = null;

    // 开始摄像头扫描
    $('#start-scan').click(function() {
        startCameraScan();
    });

    // 停止扫描
    $('#stop-scan').click(function() {
        stopScan();
    });

    // 文件扫描
    $('#scan-file').click(function() {
        const fileInput = document.getElementById('qr-file');
        if (fileInput.files.length === 0) {
            alert('请先选择一个图片文件');
            return;
        }
        scanFile(fileInput.files[0]);
    });

    // 手动输入表单提交
    $('#manual-trace-form').submit(function(e) {
        e.preventDefault();
        const batchNumber = $('#batch-number').val().trim();
        const ingredientName = $('#ingredient-name').val().trim();
        
        if (!batchNumber && !ingredientName) {
            alert('请输入批次号或食材名称');
            return;
        }
        
        // 构造查询数据
        const traceData = {
            type: 'ingredient_trace',
            batch_number: batchNumber,
            ingredient_name: ingredientName
        };
        
        processTraceData(traceData);
    });

    function startCameraScan() {
        const config = { fps: 10, qrbox: { width: 250, height: 250 } };
        
        html5QrcodeScanner = new Html5Qrcode("qr-reader");
        
        html5QrcodeScanner.start(
            { facingMode: "environment" },
            config,
            onScanSuccess,
            onScanFailure
        ).then(() => {
            $('#start-scan').hide();
            $('#stop-scan').show();
        }).catch(err => {
            console.error('启动摄像头失败:', err);
            alert('启动摄像头失败，请检查摄像头权限');
        });
    }

    function stopScan() {
        if (html5QrcodeScanner) {
            html5QrcodeScanner.stop().then(() => {
                $('#start-scan').show();
                $('#stop-scan').hide();
            });
        }
    }

    function scanFile(file) {
        const html5QrCode = new Html5Qrcode("qr-reader");
        html5QrCode.scanFile(file, true)
            .then(decodedText => {
                onScanSuccess(decodedText);
            })
            .catch(err => {
                console.error('扫描文件失败:', err);
                showError('无法识别图片中的二维码，请确保图片清晰且包含有效的二维码');
            });
    }

    function onScanSuccess(decodedText) {
        console.log('扫描成功:', decodedText);
        
        // 停止扫描
        stopScan();
        
        // 显示扫描成功信息
        $('#scan-result').show();
        $('#error-message').hide();
        
        try {
            // 尝试解析JSON数据
            const traceData = JSON.parse(decodedText);
            processTraceData(traceData);
        } catch (e) {
            // 如果不是JSON，尝试作为URL处理
            if (decodedText.startsWith('http')) {
                window.open(decodedText, '_blank');
            } else {
                // 作为普通文本处理
                processTraceData({ raw_data: decodedText });
            }
        }
    }

    function onScanFailure(error) {
        // 扫描失败时不显示错误，避免频繁提示
    }

    function processTraceData(traceData) {
        console.log('处理溯源数据:', traceData);
        
        // 发送溯源查询请求
        $.ajax({
            url: '/food-trace/qr-trace',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(traceData),
            success: function(response) {
                if (response.success) {
                    displayTraceResult(response.data);
                } else {
                    showError(response.message || '溯源查询失败');
                }
            },
            error: function(xhr) {
                console.error('溯源查询失败:', xhr);
                showError('溯源查询失败，请稍后重试');
            }
        });
    }

    function displayTraceResult(data) {
        $('#scan-result').hide();
        $('#trace-result').show();
        
        // 构建溯源结果HTML
        let html = '<div class="trace-chain">';
        
        if (data.ingredient_info) {
            html += `
                <div class="trace-step">
                    <h6><i class="fas fa-leaf"></i> 食材信息</h6>
                    <p><strong>名称：</strong>${data.ingredient_info.name}</p>
                    <p><strong>批次号：</strong>${data.ingredient_info.batch_number}</p>
                    <p><strong>入库日期：</strong>${data.ingredient_info.stock_in_date}</p>
                </div>
            `;
        }
        
        if (data.supplier_info) {
            html += `
                <div class="trace-step">
                    <h6><i class="fas fa-truck"></i> 供应商信息</h6>
                    <p><strong>供应商：</strong>${data.supplier_info.name}</p>
                    <p><strong>联系人：</strong>${data.supplier_info.contact_person || '-'}</p>
                    <p><strong>电话：</strong>${data.supplier_info.phone || '-'}</p>
                </div>
            `;
        }
        
        if (data.usage_info && data.usage_info.length > 0) {
            html += '<div class="trace-step"><h6><i class="fas fa-utensils"></i> 使用记录</h6>';
            data.usage_info.forEach(usage => {
                html += `
                    <div class="usage-record">
                        <p><strong>使用日期：</strong>${usage.consumption_date}</p>
                        <p><strong>餐次：</strong>${usage.meal_type}</p>
                        <p><strong>菜品：</strong>${usage.recipe_name || '-'}</p>
                        <p><strong>用量：</strong>${usage.quantity}${usage.unit}</p>
                    </div>
                `;
            });
            html += '</div>';
        }
        
        html += '</div>';
        
        $('#trace-content').html(html);
    }

    function showError(message) {
        $('#scan-result').hide();
        $('#trace-result').hide();
        $('#error-text').text(message);
        $('#error-message').show();
    }
});
</script>

<style nonce="{{ csp_nonce }}">
.trace-step {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
    background: #f9f9f9;
}

.trace-step h6 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.usage-record {
    border-left: 3px solid #007bff;
    padding-left: 10px;
    margin-bottom: 10px;
}

#qr-reader {
    border: 2px dashed #ddd;
    border-radius: 5px;
    padding: 20px;
}
</style>
{% endblock %}
