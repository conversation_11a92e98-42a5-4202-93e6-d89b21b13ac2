{% extends 'base.html' %}

{% block title %}
{% if ingredient %}编辑食材{% else %}添加食材{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10 col-xl-8">
            <div class="enterprise-form">
                <div class="enterprise-form-header">
                    <h1 class="enterprise-form-title">
                        <i class="fas fa-carrot me-2"></i>
                        {% if ingredient %}编辑食材信息{% else %}添加新食材{% endif %}
                    </h1>
                    <p class="enterprise-form-subtitle">
                        {% if ingredient %}修改食材的基本信息和属性{% else %}录入新食材的详细信息，建立食材档案{% endif %}
                    </p>
                </div>

                <form method="POST" enctype="multipart/form-data" class="enterprise-form-content">
                    <!-- 基本信息 -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-info-circle"></i>
                            基本信息
                        </h3>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group required">
                                    <label for="name" class="form-label">食材名称</label>
                                    <input type="text" class="form-control" id="name" name="name"
                                           value="{{ ingredient.name if ingredient else '' }}"
                                           placeholder="请输入食材名称" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="category_id" class="form-label">食材分类</label>
                                    <select class="form-select" id="category_id" name="category_id">
                                        <option value="">-- 请选择分类 --</option>
                                        {% for category in categories %}
                                        <option value="{{ category.id }}" {% if ingredient and ingredient.category_id == category.id %}selected{% endif %}>
                                            {{ category.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group required">
                                    <label for="unit" class="form-label">计量单位</label>
                                    <input type="text" class="form-control" id="unit" name="unit"
                                           value="{{ ingredient.unit if ingredient else '' }}"
                                           placeholder="如：克、千克、个、袋" required>
                                    <div class="form-text">用于库存管理和采购计算的基本单位</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="specification" class="form-label">规格说明</label>
                                    <input type="text" class="form-control" id="specification" name="specification"
                                           value="{{ ingredient.specification if ingredient else '' }}"
                                           placeholder="如：500g/袋、5kg/箱">
                                    <div class="form-text">包装规格或产品规格描述</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 存储信息 -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-warehouse"></i>
                            存储信息
                        </h3>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="storage_temp" class="form-label">存储温度</label>
                                    <input type="text" class="form-control" id="storage_temp" name="storage_temp"
                                           value="{{ ingredient.storage_temp if ingredient else '' }}"
                                           placeholder="如：2-8℃、常温">
                                    <div class="form-text">适宜的存储温度范围</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="storage_condition" class="form-label">存储条件</label>
                                    <input type="text" class="form-control" id="storage_condition" name="storage_condition"
                                           value="{{ ingredient.storage_condition if ingredient else '' }}"
                                           placeholder="如：阴凉干燥处、冷藏">
                                    <div class="form-text">具体的存储环境要求</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="shelf_life" class="form-label">保质期（天）</label>
                                    <input type="number" class="form-control" id="shelf_life" name="shelf_life"
                                           value="{{ ingredient.shelf_life if ingredient else '' }}"
                                           placeholder="如：30" min="1">
                                    <div class="form-text">从生产日期开始的保质天数</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 图片和描述 -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-image"></i>
                            图片和描述
                        </h3>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="base_image" class="form-label">食材图片</label>
                                    <div class="form-file">
                                        <input type="file" class="form-file-input" id="base_image" name="base_image" accept="image/*">
                                        <label class="form-file-label" for="base_image">
                                            <span class="form-file-text">选择图片文件...</span>
                                        </label>
                                    </div>
                                    <div class="form-text">支持 JPG、PNG 格式，建议尺寸 400x400 像素</div>
                                    {% if ingredient and ingredient.base_image %}
                                    <div class="mt-3">
                                        <div class="current-image">
                                            <label class="form-label">当前图片：</label>
                                            <div class="image-preview">
                                                <img src="{{ url_for('static', filename=ingredient.base_image) }}"
                                                     alt="{{ ingredient.name }}"
                                                     class="img-thumbnail"
                                                     style="max-width: 150px; max-height: 150px;">
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="nutrition_info" class="form-label">营养信息</label>
                                    <textarea class="form-control form-textarea" id="nutrition_info" name="nutrition_info"
                                              rows="6" placeholder="请输入营养成分信息...">{{ ingredient.nutrition_info if ingredient else '' }}</textarea>
                                    <div class="form-text">可填写蛋白质、脂肪、碳水化合物等营养成分含量</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 表单操作按钮 -->
                    <div class="form-actions">
                        <a href="{{ url_for('ingredient.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>取消
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            {% if ingredient %}更新食材{% else %}保存食材{% endif %}
                        </button>
                    </div>

                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 企业级表单增强功能

        // 文件上传显示文件名和预览
        $('.form-file-input').on('change', function() {
            var fileName = $(this).val().split('\\').pop();
            var $label = $(this).siblings('.form-file-label');
            var $text = $label.find('.form-file-text');

            if (fileName) {
                $text.text(fileName);
                $label.addClass('file-selected');

                // 图片预览
                if (this.files && this.files[0]) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        // 移除旧的预览
                        $('.new-image-preview').remove();

                        // 创建新的预览
                        var preview = $('<div class="new-image-preview mt-3">' +
                            '<label class="form-label">新图片预览：</label>' +
                            '<div class="image-preview">' +
                            '<img src="' + e.target.result + '" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">' +
                            '</div>' +
                            '</div>');

                        $('.form-file').after(preview);
                    };
                    reader.readAsDataURL(this.files[0]);
                }
            } else {
                $text.text('选择图片文件...');
                $label.removeClass('file-selected');
                $('.new-image-preview').remove();
            }
        });

        // 表单验证增强
        $('form').on('submit', function(e) {
            var isValid = true;
            var firstErrorField = null;

            // 清除之前的验证状态
            $('.form-control').removeClass('is-valid is-invalid');
            $('.invalid-feedback').remove();

            // 验证必填字段
            $('input[required], select[required], textarea[required]').each(function() {
                var $field = $(this);
                var value = $field.val().trim();

                if (!value) {
                    $field.addClass('is-invalid');
                    $field.after('<div class="invalid-feedback">此字段为必填项</div>');
                    isValid = false;
                    if (!firstErrorField) {
                        firstErrorField = $field;
                    }
                } else {
                    $field.addClass('is-valid');
                }
            });

            // 验证数字字段
            $('input[type="number"]').each(function() {
                var $field = $(this);
                var value = $field.val();

                if (value && (isNaN(value) || parseFloat(value) < 0)) {
                    $field.addClass('is-invalid');
                    $field.after('<div class="invalid-feedback">请输入有效的正数</div>');
                    isValid = false;
                    if (!firstErrorField) {
                        firstErrorField = $field;
                    }
                }
            });

            if (!isValid) {
                e.preventDefault();
                if (firstErrorField) {
                    firstErrorField.focus();
                    // 滚动到第一个错误字段
                    $('html, body').animate({
                        scrollTop: firstErrorField.offset().top - 100
                    }, 500);
                }
                toastr.error('请检查表单中的错误信息');
                return false;
            }

            // 显示提交中状态
            var $submitBtn = $(this).find('button[type="submit"]');
            var originalText = $submitBtn.html();
            $submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>保存中...');

            // 如果验证通过，恢复按钮状态（表单会提交）
            setTimeout(function() {
                $submitBtn.prop('disabled', false).html(originalText);
            }, 3000);
        });

        // 实时验证
        $('input[required], select[required], textarea[required]').on('blur', function() {
            var $field = $(this);
            var value = $field.val().trim();

            $field.removeClass('is-valid is-invalid');
            $field.siblings('.invalid-feedback').remove();

            if (!value) {
                $field.addClass('is-invalid');
                $field.after('<div class="invalid-feedback">此字段为必填项</div>');
            } else {
                $field.addClass('is-valid');
            }
        });

        // 数字字段实时验证
        $('input[type="number"]').on('input', function() {
            var $field = $(this);
            var value = $field.val();

            $field.removeClass('is-valid is-invalid');
            $field.siblings('.invalid-feedback').remove();

            if (value && (isNaN(value) || parseFloat(value) < 0)) {
                $field.addClass('is-invalid');
                $field.after('<div class="invalid-feedback">请输入有效的正数</div>');
            } else if (value) {
                $field.addClass('is-valid');
            }
        });

        // 字段焦点效果
        $('.form-control, .form-select').on('focus', function() {
            $(this).closest('.form-group').addClass('focused');
        }).on('blur', function() {
            $(this).closest('.form-group').removeClass('focused');
        });

        // 自动保存草稿功能（可选）
        var formData = {};
        var autoSaveTimer;

        function saveFormData() {
            $('input, select, textarea').each(function() {
                if ($(this).attr('name') && $(this).attr('name') !== 'csrf_token') {
                    formData[$(this).attr('name')] = $(this).val();
                }
            });
            localStorage.setItem('ingredient_form_draft', JSON.stringify(formData));
        }

        function loadFormData() {
            var savedData = localStorage.getItem('ingredient_form_draft');
            if (savedData) {
                try {
                    var data = JSON.parse(savedData);
                    Object.keys(data).forEach(function(key) {
                        var $field = $('[name="' + key + '"]');
                        if ($field.length && !$field.val()) {
                            $field.val(data[key]);
                        }
                    });
                } catch (e) {
                    console.log('无法加载草稿数据');
                }
            }
        }

        // 页面加载时尝试加载草稿
        {% if not ingredient %}
        loadFormData();
        {% endif %}

        // 表单变化时自动保存草稿
        $('input, select, textarea').on('input change', function() {
            clearTimeout(autoSaveTimer);
            autoSaveTimer = setTimeout(saveFormData, 1000);
        });

        // 表单提交成功后清除草稿
        $('form').on('submit', function() {
            localStorage.removeItem('ingredient_form_draft');
        });
    });
</script>

<style>
/* 表单页面专用样式增强 */
.form-file-label.file-selected {
    border-color: #2c5aa0;
    background-color: #f8f9fa;
}

.form-group.focused {
    transform: translateY(-1px);
    transition: transform 0.2s ease;
}

.image-preview {
    border: 2px dashed #dee2e6;
    border-radius: 4px;
    padding: 8px;
    text-align: center;
    background-color: #f8f9fa;
}

.current-image .image-preview {
    border-color: #198754;
    background-color: #f8fff9;
}

.new-image-preview .image-preview {
    border-color: #0dcaf0;
    background-color: #f0fcff;
}

.form-section {
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.enterprise-form-content {
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
</style>
{% endblock %}
