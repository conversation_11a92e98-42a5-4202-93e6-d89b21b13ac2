{% extends 'base.html' %}

{% block title %}食材管理{% endblock %}

{% block content %}
<div class="card">
                <div class="card-header">
                    <!-- 桌面端布局 -->
                    <div class="d-flex justify-content-between align-items-center desktop-only">
                        <div>
                            <h3 class="card-title mb-0">
                                <i class="fas fa-carrot me-2"></i>食材管理
                            </h3>
                            <small class="text-muted">管理所有食材信息和营养数据</small>
                        </div>
                        <div class="card-tools">
                            <div class="btn-group">
                                <!-- 显示模式切换 -->
                                <a href="{{ url_for('ingredient.index', view_mode='list', category_id=category_id, keyword=keyword) }}"
                                   class="btn btn-sm {{ 'btn-primary' if view_mode == 'list' else 'btn-outline-primary' }}">
                                    <i class="fas fa-list"></i> 列表
                                </a>
                                <a href="{{ url_for('ingredient.index', view_mode='category', category_id=category_id, keyword=keyword) }}"
                                   class="btn btn-sm {{ 'btn-primary' if view_mode == 'category' else 'btn-outline-primary' }}">
                                    <i class="fas fa-th-large"></i> 分类
                                </a>
                                <a href="{{ url_for('ingredient.create') }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> 添加食材
                                </a>
                                <button class="btn btn-outline-secondary btn-sm" onclick="exportData()">
                                    <i class="fas fa-download"></i> 批量导出
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 移动端布局 -->
                    <div class="mobile-only">
                        <div class="row">
                            <div class="col-12">
                                <h3 class="card-title mb-2">
                                    <i class="fas fa-carrot me-1"></i>食材管理
                                </h3>
                                <div class="action-buttons">
                                    <a href="{{ url_for('ingredient.create') }}" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> 添加食材
                                    </a>
                                    <a href="{{ url_for('ingredient.index', view_mode='list', category_id=category_id, keyword=keyword) }}"
                                       class="btn {{ 'btn-primary' if view_mode == 'list' else 'btn-outline-primary' }}">
                                        <i class="fas fa-list"></i> 列表
                                    </a>
                                    <a href="{{ url_for('ingredient.index', view_mode='category', category_id=category_id, keyword=keyword) }}"
                                       class="btn {{ 'btn-primary' if view_mode == 'category' else 'btn-outline-primary' }}">
                                        <i class="fas fa-th-large"></i> 分类
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 企业级食材列表 -->
                    <div class="enterprise-table-container desktop-only">
                        <div class="table-toolbar">
                            <div class="table-toolbar-left">
                                <div class="table-search">
                                    <i class="fas fa-search search-icon"></i>
                                    <input type="text" class="form-control" placeholder="快速搜索食材..." id="quickSearch">
                                </div>
                                <span class="text-muted">共 {{ pagination.total }} 条记录</span>
                            </div>
                            <div class="table-toolbar-right">
                                <button class="btn btn-outline-secondary btn-sm" onclick="exportData()">
                                    <i class="fas fa-download"></i> 导出
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="refreshTable()">
                                    <i class="fas fa-sync"></i> 刷新
                                </button>
                            </div>
                        </div>

                        <table class="enterprise-table">
                            <thead>
                                <tr>
                                    <th class="sortable number-col">ID</th>
                                    <th class="text-center">图片</th>
                                    <th class="sortable">名称</th>
                                    <th class="sortable">分类</th>
                                    <th>规格</th>
                                    <th>单位</th>
                                    <th>存储条件</th>
                                    <th class="number-col sortable">保质期(天)</th>
                                    <th class="status-col">状态</th>
                                    <th class="action-col">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ingredient in ingredients %}
                                <tr>
                                    <td class="number-col">{{ ingredient.id }}</td>
                                    <td class="text-center">
                                        {% if ingredient.base_image %}
                                        <img src="{{ url_for('static', filename=ingredient.base_image) }}"
                                             alt="{{ ingredient.name }}"
                                             class="table-img"
                                             onerror="this.src='{{ url_for('static', filename='img/qr-code-placeholder.png') }}'; this.onerror=null;">
                                        {% else %}
                                        <img src="{{ url_for('static', filename='img/qr-code-placeholder.png') }}"
                                             alt="No Image" class="table-img">
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>{{ ingredient.name }}</strong>
                                        {% if ingredient.description %}
                                        <div class="small text-muted">{{ ingredient.description|truncate(30) }}</div>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if ingredient.category_rel %}
                                            <span class="badge badge-secondary">{{ ingredient.category_rel.name }}</span>
                                        {% else %}
                                            <span class="badge badge-light">{{ ingredient.category or '未分类' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ ingredient.specification or '-' }}</td>
                                    <td><span class="badge badge-info">{{ ingredient.unit }}</span></td>
                                    <td>{{ ingredient.storage_condition or ingredient.storage_temp or '-' }}</td>
                                    <td class="number-col">{{ ingredient.shelf_life or '-' }}</td>
                                    <td class="status-col">
                                        {% if ingredient.status == 1 %}
                                        <span class="badge badge-success">启用</span>
                                        {% else %}
                                        <span class="badge badge-danger">停用</span>
                                        {% endif %}
                                    </td>
                                    <td class="action-col">
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('ingredient.view', id=ingredient.id) }}"
                                               class="btn btn-outline-info btn-xs" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if current_user.is_admin() %}
                                            <!-- 超级管理员：可以编辑删除所有食材 -->
                                            <a href="{{ url_for('ingredient.edit', id=ingredient.id) }}"
                                               class="btn btn-outline-primary btn-xs" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-outline-danger btn-xs delete-btn"
                                                    data-id="{{ ingredient.id }}" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% elif ingredient.is_global %}
                                            <!-- 系统食材：普通用户不能编辑删除 -->
                                            <span class="badge badge-info btn-xs">系统</span>
                                            {% elif ingredient.area_id and ingredient.area_id == current_user.get_current_area().id %}
                                            <!-- 本校食材：可以编辑删除 -->
                                            <a href="{{ url_for('ingredient.edit', id=ingredient.id) }}"
                                               class="btn btn-outline-primary btn-xs" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-outline-danger btn-xs delete-btn"
                                                    data-id="{{ ingredient.id }}" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% else %}
                                            <!-- 其他学校食材：不能编辑删除 -->
                                            <span class="badge badge-secondary btn-xs">其他</span>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="10" class="table-empty">
                                        <div class="table-empty-icon">
                                            <i class="fas fa-carrot"></i>
                                        </div>
                                        <div class="table-empty-text">暂无食材数据</div>
                                        <div class="table-empty-subtext">您可以添加新的食材或调整筛选条件</div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>

                        {% if pagination.pages > 1 %}
                        <div class="table-pagination">
                            <div class="pagination-info">
                                显示第 {{ (pagination.page - 1) * pagination.per_page + 1 }} -
                                {{ (pagination.page * pagination.per_page) if (pagination.page * pagination.per_page < pagination.total) else pagination.total }}
                                条，共 {{ pagination.total }} 条记录
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm">
                                    {% if pagination.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('ingredient.index', page=pagination.prev_num, per_page=pagination.per_page, category_id=category_id, keyword=keyword) }}">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                                    </li>
                                    {% endif %}

                                    {% for page_num in pagination.iter_pages() %}
                                        {% if page_num %}
                                            {% if page_num == pagination.page %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ page_num }}</span>
                                            </li>
                                            {% else %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('ingredient.index', page=page_num, per_page=pagination.per_page, category_id=category_id, keyword=keyword) }}">{{ page_num }}</a>
                                            </li>
                                            {% endif %}
                                        {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if pagination.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('ingredient.index', page=pagination.next_num, per_page=pagination.per_page, category_id=category_id, keyword=keyword) }}">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                        {% endif %}
                    </div>


                </div>
            </div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                确定要删除这个食材吗？此操作不可逆。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 删除功能
        var deleteId = null;

        $('.delete-btn').click(function() {
            deleteId = $(this).data('id');
            $('#deleteModal').modal('show');
        });

        $('#confirmDelete').click(function() {
            if (deleteId) {
                $.ajax({
                    url: '{{ url_for("ingredient.delete", id=0) }}'.replace('0', deleteId),
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#deleteModal').modal('hide');
                    },
                    error: function() {
                        toastr.error('删除失败，请稍后重试！');
                        $('#deleteModal').modal('hide');
                    }
                });
            }
        });

        // 企业级表格增强功能

        // 快速搜索功能
        $('#quickSearch').on('input', function() {
            var searchTerm = $(this).val().toLowerCase();
            $('.enterprise-table tbody tr').each(function() {
                var rowText = $(this).text().toLowerCase();
                if (rowText.indexOf(searchTerm) === -1) {
                    $(this).hide();
                } else {
                    $(this).show();
                }
            });
        });

        // 表格排序功能
        $('.enterprise-table th.sortable').click(function() {
            var $this = $(this);
            var column = $this.index();
            var $table = $this.closest('table');
            var $tbody = $table.find('tbody');
            var rows = $tbody.find('tr').toArray();

            // 移除其他列的排序状态
            $this.siblings().removeClass('sort-asc sort-desc');

            // 切换当前列的排序状态
            var isAsc = $this.hasClass('sort-asc');
            $this.removeClass('sort-asc sort-desc');
            $this.addClass(isAsc ? 'sort-desc' : 'sort-asc');

            // 排序行
            rows.sort(function(a, b) {
                var aText = $(a).find('td').eq(column).text().trim();
                var bText = $(b).find('td').eq(column).text().trim();

                // 数字排序
                if (!isNaN(aText) && !isNaN(bText)) {
                    return isAsc ? bText - aText : aText - bText;
                }

                // 文本排序
                if (isAsc) {
                    return bText.localeCompare(aText);
                } else {
                    return aText.localeCompare(bText);
                }
            });

            // 重新排列行
            $.each(rows, function(index, row) {
                $tbody.append(row);
            });
        });

        // 表格行悬停效果增强
        $('.enterprise-table tbody tr').hover(
            function() {
                $(this).addClass('table-row-hover');
            },
            function() {
                $(this).removeClass('table-row-hover');
            }
        );

        // 工具栏功能
        window.exportData = function() {
            toastr.info('导出功能开发中...');
        };

        window.refreshTable = function() {
            window.location.reload();
        };

        // 表格加载动画
        function showTableLoading() {
            $('.enterprise-table-container').addClass('table-loading');
        }

        function hideTableLoading() {
            $('.enterprise-table-container').removeClass('table-loading');
        }

        // 图片加载错误处理
        $('.table-img').on('error', function() {
            $(this).attr('src', '{{ url_for("static", filename="img/qr-code-placeholder.png") }}');
        });
    });
</script>
{% endblock %}
