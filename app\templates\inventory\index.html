{% extends 'base.html' %}

{% block title %}库存管理{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
/* 库存管理页面样式 - 与入库单页面保持一致 */
.compact-toolbar {
    background: #f8f9fa;
    padding: 15px 20px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
    margin-bottom: 20px;
}

.filter-collapse {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.inventory-number {
    font-weight: 600;
    color: #007bff;
    font-size: 15px;
}

.btn-group-compact {
    display: flex;
    gap: 3px;
    flex-wrap: wrap;
}

.btn-xs {
    padding: 3px 8px;
    font-size: 12px;
    line-height: 1.3;
}

.table-compact {
    font-size: 13px;
    line-height: 1.4;
}

.table-compact th {
    padding: 12px 16px;
    background: linear-gradient(135deg, var(--theme-gray-100, #f8f9fa) 0%, var(--theme-gray-200, #e9ecef) 100%);
    font-weight: 600;
    font-size: 13px;
    color: var(--theme-gray-800, #343a40);
    border-bottom: 2px solid var(--theme-gray-300, #dee2e6);
    border-right: 1px solid var(--theme-gray-300, #dee2e6);
    text-align: left;
    vertical-align: middle;
    white-space: nowrap;
}

.table-compact td {
    padding: 12px 16px;
    vertical-align: middle;
    font-size: 13px;
    line-height: 1.4;
    border-right: 1px solid var(--theme-gray-200, #e9ecef);
}

.badge-sm {
    font-size: 11px;
    padding: 3px 8px;
}

.ingredient-highlight {
    font-weight: 600;
    color: #2c3e50;
    font-size: 15px;
}

.ingredient-category {
    font-size: 11px;
    color: #6c757d;
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
    margin-top: 2px;
}

.stock-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 4px;
}

.stock-indicator.sufficient { background-color: #28a745; }
.stock-indicator.low { background-color: #ffc107; }
.stock-indicator.critical { background-color: #dc3545; }
.stock-indicator.expired { background-color: #6c757d; }

/* 响应式设计 */
@media (max-width: 768px) {
    .compact-toolbar {
        padding: 10px 15px;
    }

    .table-compact {
        font-size: 14px;
    }

    .table-compact th,
    .table-compact td {
        padding: 8px 6px;
    }
}
</style>
{% endblock %}

{% block content %}



<!-- 企业级页面容器 -->
<div class="enterprise-table-v2-container">
    <!-- 现代化页面头部 -->
    <div class="smart-toolbar">
        <div class="toolbar-left">
            <h5 class="mb-0"><i class="fas fa-warehouse me-2 text-primary"></i>库存管理</h5>
            <p class="text-muted mb-0 small">实时监控库存状态，确保食材新鲜安全</p>
        </div>
        <div class="toolbar-right">
            <div class="view-toggle me-3">
                <div class="btn-group" role="group">
                    <a href="{{ url_for('inventory.index', view_type='detail') }}"
                       class="btn btn-outline-primary btn-sm {{ 'active' if view_type == 'detail' else '' }}">
                        <i class="fas fa-list"></i> 详细
                    </a>
                    <a href="{{ url_for('inventory.index', view_type='summary') }}"
                       class="btn btn-outline-primary btn-sm {{ 'active' if view_type == 'summary' else '' }}">
                        <i class="fas fa-chart-bar"></i> 汇总
                    </a>
                </div>
            </div>
            <div class="action-buttons">
                <div class="btn-group" role="group">
                    <a href="{{ url_for('inventory.check_expiry') }}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-exclamation-triangle"></i> 临期检查
                    </a>
                    <a href="{{ url_for('stock_out.index') }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-sign-out-alt"></i> 出库
                    </a>
                    <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-calendar-alt"></i> 菜单计划
                    </a>
                    <a href="{{ url_for('inventory.statistics') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-chart-line"></i> 统计分析
                    </a>
                    <button class="btn btn-outline-primary btn-sm" onclick="printInventory()">
                        <i class="fas fa-print"></i> 打印库存
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" data-toggle="collapse" data-target="#filterForm">
                        <i class="fas fa-filter"></i> 筛选
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 可折叠的筛选区域 -->
    <div class="collapse mb-3" id="filterForm">
        <div class="filter-collapse">
            <form method="get" action="{{ url_for('inventory.index') }}">
                <input type="hidden" name="view_type" value="{{ view_type }}">

                <!-- 桌面端筛选布局 -->
                <div class="row desktop-only">
                    <div class="col-md-2">
                        <select name="storage_location_id" class="form-control form-control-sm" id="storage_location_id">
                            <option value="">全部位置</option>
                            {% for location in storage_locations %}
                            <option value="{{ location.id }}" {% if storage_location_id == location.id %}selected{% endif %}>{{ location.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="ingredient_id" class="form-control form-control-sm">
                            <option value="">全部食材</option>
                            {% for ingredient in ingredients %}
                            <option value="{{ ingredient.id }}" {% if ingredient_id == ingredient.id %}selected{% endif %}>{{ ingredient.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="status" class="form-control form-control-sm">
                            <option value="">全部状态</option>
                            <option value="正常" {% if status == '正常' %}selected{% endif %}>正常</option>
                            <option value="待检" {% if status == '待检' %}selected{% endif %}>待检</option>
                            <option value="冻结" {% if status == '冻结' %}selected{% endif %}>冻结</option>
                            <option value="已过期" {% if status == '已过期' %}selected{% endif %}>已过期</option>
                            <option value="已用完" {% if status == '已用完' %}selected{% endif %}>已用完</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="expiry_days" class="form-control form-control-sm">
                            <option value="">全部期限</option>
                            <option value="7" {% if expiry_days == 7 %}selected{% endif %}>7天内过期</option>
                            <option value="15" {% if expiry_days == 15 %}selected{% endif %}>15天内过期</option>
                            <option value="30" {% if expiry_days == 30 %}selected{% endif %}>30天内过期</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary btn-sm mr-1">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times"></i> 清除
                        </a>
                    </div>
                </div>

                <!-- 移动端筛选布局 -->
                <div class="mobile-only filter-section" style="display: none;">
                    <div class="filter-row">
                        <select name="storage_location_id" class="form-control" id="storage_location_id_mobile">
                            <option value="">全部位置</option>
                            {% for location in storage_locations %}
                            <option value="{{ location.id }}" {% if storage_location_id == location.id %}selected{% endif %}>{{ location.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="filter-row">
                        <select name="ingredient_id" class="form-control">
                            <option value="">全部食材</option>
                            {% for ingredient in ingredients %}
                            <option value="{{ ingredient.id }}" {% if ingredient_id == ingredient.id %}selected{% endif %}>{{ ingredient.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="filter-row">
                        <select name="status" class="form-control">
                            <option value="">全部状态</option>
                            <option value="正常" {% if status == '正常' %}selected{% endif %}>正常</option>
                            <option value="待检" {% if status == '待检' %}selected{% endif %}>待检</option>
                            <option value="冻结" {% if status == '冻结' %}selected{% endif %}>冻结</option>
                            <option value="已过期" {% if status == '已过期' %}selected{% endif %}>已过期</option>
                            <option value="已用完" {% if status == '已用完' %}selected{% endif %}>已用完</option>
                        </select>
                    </div>
                    <div class="filter-row">
                        <select name="expiry_days" class="form-control">
                            <option value="">全部期限</option>
                            <option value="7" {% if expiry_days == 7 %}selected{% endif %}>7天内过期</option>
                            <option value="15" {% if expiry_days == 15 %}selected{% endif %}>15天内过期</option>
                            <option value="30" {% if expiry_days == 30 %}selected{% endif %}>30天内过期</option>
                        </select>
                    </div>
                    <div class="filter-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> 清除
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 企业级表格 -->
    <div class="table-responsive">
        <table class="enterprise-table-v2">
            <thead>
                <tr>
                    <th style="width: 15%;">🥬 食材名称</th>
                    <th style="width: 10%;">供应商</th>
                    <th style="width: 12%;">存储位置</th>
                    <th style="width: 12%;">批次号</th>
                    <th style="width: 8%;">数量</th>
                    <th style="width: 6%;">单位</th>
                    <th style="width: 10%;">生产日期</th>
                    <th style="width: 10%;">过期日期</th>
                    <th style="width: 8%;">状态</th>
                    <th style="width: 9%;">操作</th>
                </tr>
            </thead>
                        <tbody>
                            {% for inventory in inventories %}
                            <tr>
                                <td>
                                    <div class="ingredient-highlight">{{ inventory.ingredient.name }}</div>
                                    {% if inventory.ingredient.category %}
                                    <small class="ingredient-category">{{ inventory.ingredient.category.name }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <small>{{ inventory.supplier.name if inventory.supplier else '-' }}</small>
                                </td>
                                <td>
                                    <small>{{ inventory.storage_location.name }}</small>
                                    <br><small class="text-muted">({{ inventory.storage_location.location_code }})</small>
                                </td>
                                <td>
                                    <small class="text-monospace">{{ inventory.batch_number }}</small>
                                </td>
                                <td class="text-right">
                                    <strong class="text-primary">{{ inventory.quantity }}</strong>
                                </td>
                                <td>
                                    <small>{{ inventory.unit }}</small>
                                </td>
                                <td>
                                    <small>{{ inventory.production_date|format_datetime('%m-%d') if inventory.production_date else '-' }}</small>
                                </td>
                                <td>
                                    <small>{{ inventory.expiry_date|format_datetime('%m-%d') if inventory.expiry_date else '-' }}</small>
                                    {% if inventory.status == '已过期' %}
                                        <br><span class="badge badge-danger badge-sm">已过期</span>
                                    {% elif inventory.status == '临期' %}
                                        <br><span class="badge badge-warning badge-sm">临期</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if inventory.status == '正常' %}
                                        <span class="status-badge status-success">正常</span>
                                    {% elif inventory.status == '待检' %}
                                        <span class="status-badge status-warning">待检</span>
                                    {% elif inventory.status == '冻结' %}
                                        <span class="status-badge status-info">冻结</span>
                                    {% elif inventory.status == '已过期' %}
                                        <span class="status-badge status-error">已过期</span>
                                    {% elif inventory.status == '已用完' %}
                                        <span class="status-badge status-secondary">已用完</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{{ url_for('inventory.detail', id=inventory.id) }}"
                                           class="btn btn-outline-primary btn-sm" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-secondary btn-sm"
                                                onclick="printInventoryItem({{ inventory.id }})" title="打印标签">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <i class="fas fa-box-open text-muted"></i>
                                    <br><small class="text-muted">暂无库存数据</small>
                                </td>
                            </tr>
                            {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- 移动端卡片视图 -->
    <div class="mobile-only">
        {% for inventory in inventories %}
        <div class="card mb-3 border-left-{% if inventory.status == '正常' %}success{% elif inventory.status == '待检' %}warning{% elif inventory.status == '冻结' %}info{% elif inventory.status == '已过期' %}danger{% else %}secondary{% endif %}">
            <div class="card-body py-2">
                <div class="row">
                    <div class="col-8">
                        <h6 class="mb-1">🥬 {{ inventory.ingredient.name }}</h6>
                        {% if inventory.ingredient.category %}
                        <small class="text-muted">{{ inventory.ingredient.category.name }}</small>
                        {% endif %}
                    </div>
                    <div class="col-4 text-right">
                        {% if inventory.status == '正常' %}
                        <span class="badge badge-success">{{ inventory.status }}</span>
                        {% elif inventory.status == '待检' %}
                        <span class="badge badge-warning">{{ inventory.status }}</span>
                        {% elif inventory.status == '冻结' %}
                        <span class="badge badge-info">{{ inventory.status }}</span>
                        {% elif inventory.status == '已过期' %}
                        <span class="badge badge-danger">{{ inventory.status }}</span>
                        {% else %}
                        <span class="badge badge-secondary">{{ inventory.status }}</span>
                        {% endif %}
                    </div>
                </div>

                <div class="row mt-2">
                    <div class="col-6">
                        <small class="text-muted">数量</small>
                        <div class="font-weight-bold text-primary">{{ inventory.quantity }} {{ inventory.unit }}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">批次号</small>
                        <div class="text-monospace small">{{ inventory.batch_number }}</div>
                    </div>
                </div>

                <div class="row mt-2">
                    <div class="col-6">
                        <small class="text-muted">供应商</small>
                        <div class="small">{{ inventory.supplier.name if inventory.supplier else '-' }}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">存储位置</small>
                        <div class="small">{{ inventory.storage_location.name }}</div>
                    </div>
                </div>

                <div class="row mt-2">
                    <div class="col-6">
                        <small class="text-muted">生产日期</small>
                        <div class="small">{{ inventory.production_date|format_datetime('%m-%d') }}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">过期日期</small>
                        <div class="small">
                            {{ inventory.expiry_date|format_datetime('%m-%d') }}
                            {% if inventory.status == '已过期' %}
                            <span class="badge badge-danger badge-sm ml-1">已过期</span>
                            {% elif inventory.status == '临期' %}
                            <span class="badge badge-warning badge-sm ml-1">临期</span>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row mt-2">
                    <div class="col-12">
                        <a href="{{ url_for('inventory.detail', id=inventory.id) }}" class="btn btn-outline-info btn-sm btn-block">
                            <i class="fas fa-eye"></i> 查看详情
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
            <h5>暂无库存数据</h5>
            <p class="text-muted">您可以调整筛选条件或检查入库记录</p>
        </div>
        {% endfor %}
    </div>

    <!-- 精简分页 -->
    {% if pagination.pages > 1 %}
    <div class="d-flex justify-content-center mt-3">
        <ul class="pagination pagination-sm">
            {% if pagination.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('inventory.index', page=pagination.prev_num, ingredient_id=ingredient_id, status=status, expiry_days=expiry_days, storage_location_id=storage_location_id, view_type=view_type) }}">
                    «
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">«</span>
            </li>
            {% endif %}

            {% for page in pagination.iter_pages() %}
                {% if page %}
                    {% if page != pagination.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('inventory.index', page=page, ingredient_id=ingredient_id, status=status, expiry_days=expiry_days, storage_location_id=storage_location_id, view_type=view_type) }}">
                            {{ page }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}

            {% if pagination.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('inventory.index', page=pagination.next_num, ingredient_id=ingredient_id, status=status, expiry_days=expiry_days, storage_location_id=storage_location_id, view_type=view_type) }}">
                    »
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">»</span>
            </li>
            {% endif %}
        </ul>
    </div>
    {% endif %}

</div> <!-- 关闭企业级容器 -->

{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    // 加载存储位置（已移除仓库选择器，此函数保留以防其他地方调用）
    function loadStorageLocations() {
        // 由于每个学校只有一个仓库，不再需要动态加载存储位置
        console.log('loadStorageLocations called but no longer needed');
    }

    // 打印库存报表
    function printInventory() {
        // 获取当前筛选参数
        const params = new URLSearchParams(window.location.search);
        const printUrl = "{{ url_for('inventory.print_inventory') }}" + "?" + params.toString();
        window.open(printUrl, '_blank');
    }

    // 打印单个库存项标签
    function printInventoryItem(inventoryId) {
        window.open("{{ url_for('inventory.print_item_label', id=0) }}".replace('0', inventoryId), '_blank');
    }
</script>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/comprehensive-event-handler.js') }}"></script>
{% endblock %}
