{% extends "base.html" %}

{% block title %}库存统计分析{% endblock %}

{% block content %}
<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-chart-bar text-primary"></i> 库存统计分析</h2>
            {% if warehouse %}
            <p class="text-muted mb-0">
                <i class="fas fa-warehouse"></i> 当前仓库：{{ warehouse.name }}
                {% if storage_locations %}
                <span class="ml-3"><i class="fas fa-map-marker-alt"></i> 储存位置：{{ storage_locations|length }} 个</span>
                {% endif %}
            </p>
            {% endif %}
        </div>
        <div class="btn-group">
            <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> 返回库存
            </a>
        </div>
    </div>

    <!-- 筛选条件 -->
    <div class="card mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="fas fa-filter text-primary"></i> 筛选条件</h5>
        </div>
        <div class="card-body">
            <form id="filterForm">
                <!-- 第一行：基础筛选 -->
                <div class="row mb-3">
                    <div class="col-lg-3 col-md-6">
                        <label for="stat_type" class="form-label font-weight-bold">
                            <i class="fas fa-chart-line text-info"></i> 统计类型
                        </label>
                        <select class="form-control" id="stat_type" name="type">
                            <option value="ingredient">🥬 按食材统计</option>
                            <option value="ingredient_category">📊 按分类汇总</option>
                            <option value="supplier">🏪 按供应商统计</option>
                        </select>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <label for="start_date" class="form-label font-weight-bold">
                            <i class="fas fa-calendar-alt text-success"></i> 开始日期
                        </label>
                        <input type="date" class="form-control" id="start_date" name="start_date">
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <label for="end_date" class="form-label font-weight-bold">
                            <i class="fas fa-calendar-check text-success"></i> 结束日期
                        </label>
                        <input type="date" class="form-control" id="end_date" name="end_date">
                    </div>
                    <div class="col-lg-3 col-md-6 d-flex align-items-end">
                        <button type="button" class="btn btn-primary btn-lg btn-block" onclick="loadStatistics()">
                            <i class="fas fa-search"></i> 开始查询
                        </button>
                    </div>
                </div>

                <!-- 第二行：高级筛选 -->
                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <label for="storage_location_id" class="form-label font-weight-bold">
                            <i class="fas fa-map-marker-alt text-warning"></i> 储存位置
                        </label>
                        <select class="form-control" id="storage_location_id" name="storage_location_id">
                            <option value="">📦 全部位置</option>
                            {% for location in storage_locations %}
                            <option value="{{ location.id }}">{{ location.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <label for="category_id" class="form-label font-weight-bold">
                            <i class="fas fa-tags text-info"></i> 食材分类
                        </label>
                        <select class="form-control" id="category_id" name="category_id">
                            <option value="">🏷️ 全部分类</option>
                            {% if category_groups %}
                                {% for group_name, group_categories in category_groups.items() %}
                                    {% if group_categories %}
                                    <optgroup label="📂 {{ group_name }}">
                                        {% for category in group_categories %}
                                        <option value="{{ category.id }}">{{ category.name }}</option>
                                        {% endfor %}
                                    </optgroup>
                                    {% endif %}
                                {% endfor %}
                            {% else %}
                                {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            {% endif %}
                        </select>
                    </div>
                    <div class="col-lg-3 col-md-6" id="supplierCol">
                        <label for="supplier_id" class="form-label font-weight-bold">
                            <i class="fas fa-truck text-primary"></i> 供应商
                        </label>
                        <select class="form-control" id="supplier_id" name="supplier_id">
                            <option value="">🏪 全部供应商</option>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-lg-3 col-md-6 d-flex align-items-end">
                        <button type="button" class="btn btn-outline-secondary btn-block" onclick="resetFilters()">
                            <i class="fas fa-undo"></i> 重置条件
                        </button>
                    </div>
                </div>

                <!-- 快捷时间选择 -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="d-flex flex-wrap align-items-center">
                            <small class="text-muted mr-2">⏰ 快捷时间：</small>
                            <button type="button" class="btn btn-outline-primary btn-sm mr-1 mb-1" onclick="setDateRange(7)">最近7天</button>
                            <button type="button" class="btn btn-outline-primary btn-sm mr-1 mb-1" onclick="setDateRange(30)">最近30天</button>
                            <button type="button" class="btn btn-outline-primary btn-sm mr-1 mb-1" onclick="setDateRange(90)">最近3个月</button>
                            <button type="button" class="btn btn-outline-primary btn-sm mr-1 mb-1" onclick="setCurrentMonth()">本月</button>
                            <button type="button" class="btn btn-outline-primary btn-sm mr-3 mb-1" onclick="setLastMonth()">上月</button>
                        </div>
                    </div>
                </div>

                <!-- 快捷食材分类选择 -->
                <div class="row mt-2" id="categoryQuickSelect">
                    <div class="col-12">
                        <div class="d-flex flex-wrap align-items-center">
                            <small class="text-muted mr-2">🥬 快捷分类：</small>
                            <button type="button" class="btn btn-outline-success btn-sm mr-1 mb-1" onclick="setCategoryGroup('肉类', this)">🥩 肉类</button>
                            <button type="button" class="btn btn-outline-success btn-sm mr-1 mb-1" onclick="setCategoryGroup('蔬菜', this)">🥬 蔬菜</button>
                            <button type="button" class="btn btn-outline-success btn-sm mr-1 mb-1" onclick="setCategoryGroup('调味品', this)">🍯 调味品</button>
                            <button type="button" class="btn btn-outline-success btn-sm mr-1 mb-1" onclick="setCategoryGroup('其他', this)">📦 其他</button>
                            <button type="button" class="btn btn-outline-secondary btn-sm mr-1 mb-1" onclick="setCategoryGroup('', this)">📋 全部分类</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 统计结果 -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-table"></i> 统计结果</h5>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-success" onclick="exportData()">
                    <i class="fas fa-file-excel"></i> 导出Excel
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="printStatistics()">
                    <i class="fas fa-print"></i> 打印报表
                </button>
            </div>
        </div>
        <div class="card-body">
            <!-- 加载提示 -->
            <div id="loadingDiv" class="text-center py-5" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">
                    <i class="fas fa-database"></i> 正在查询数据库，请稍候...
                    <br><small>系统正在为您分析库存数据</small>
                </p>
            </div>

            <!-- 友好提示 -->
            <div id="welcomeDiv" class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                    <h4 class="text-muted">📊 智能库存统计分析</h4>
                    <p class="text-muted mb-4">
                        欢迎使用库存统计分析功能！您可以：<br>
                        • 🥬 按食材分析库存周转情况<br>
                        • 📊 按分类汇总各类食材数据<br>
                        • 🏪 按供应商统计合作数据
                    </p>
                    <div class="alert alert-info d-inline-block">
                        <i class="fas fa-lightbulb"></i>
                        <strong>小贴士：</strong>已为您设置默认查询条件（最近30天），点击"开始查询"即可查看统计结果
                    </div>
                </div>
            </div>

            <!-- 统计图表 -->
            <div id="chartContainer" style="display: none;">
                <canvas id="statisticsChart" width="400" height="200"></canvas>
            </div>

            <!-- 统计表格 -->
            <div id="tableContainer" style="display: none;">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="statisticsTable">
                        <thead class="table-dark">
                            <!-- 表头将通过JavaScript动态生成 -->
                        </thead>
                        <tbody>
                            <!-- 数据将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 空数据提示 -->
            <div id="noDataDiv" class="text-center py-5" style="display: none;">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无统计数据</p>
                <p class="text-muted">请调整筛选条件后重新查询</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script nonce="{{ csp_nonce }}">
    let currentChart = null;
    let currentData = null;

    // 存储食材分类组数据
    let categoryGroups = {};
    try {
        categoryGroups = {{ category_groups_json|tojson|safe if category_groups_json else '{}' }};
    } catch (e) {
        console.error('分类组数据解析失败:', e);
        categoryGroups = {};
    }

    // 页面加载时设置默认日期
    document.addEventListener('DOMContentLoaded', function() {
        // 设置默认日期为最近30天
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);

        document.getElementById('start_date').value = startDate.toISOString().split('T')[0];
        document.getElementById('end_date').value = endDate.toISOString().split('T')[0];

        // 监听统计类型变化，显示/隐藏食材分类快捷选择
        document.getElementById('stat_type').addEventListener('change', function() {
            const statType = this.value;
            const categoryQuickSelect = document.getElementById('categoryQuickSelect');

            if (statType === 'ingredient' || statType === 'ingredient_category') {
                categoryQuickSelect.style.display = 'block';
            } else {
                categoryQuickSelect.style.display = 'none';
            }
        });

        // 初始化时显示食材分类快捷选择（默认是按食材统计）
        document.getElementById('categoryQuickSelect').style.display = 'block';

        // 不自动加载统计，让用户主动点击查询
        showWelcome();
    });

    // 加载统计数据 - 优化版本
    function loadStatistics() {
        const formData = new FormData(document.getElementById('filterForm'));
        const params = new URLSearchParams(formData);

        // 显示加载状态
        showLoading();

        // 设置超时时间为60秒
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 60000);

        fetch(`{{ url_for('inventory.statistics_data') }}?${params}`, {
            signal: controller.signal,
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        })
            .then(response => {
                clearTimeout(timeoutId);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(result => {
                hideLoading();
                if (result.success) {
                    currentData = result.data;
                    displayStatistics(result.data, formData.get('type'));
                } else {
                    showError(result.message || '查询失败，请重试');
                }
            })
            .catch(error => {
                clearTimeout(timeoutId);
                hideLoading();
                if (error.name === 'AbortError') {
                    showError('查询超时，请缩小查询范围后重试');
                } else {
                    showError('加载统计数据失败: ' + error.message);
                }
            });
    }

    // 显示统计结果
    function displayStatistics(data, type) {
        if (!data || (Array.isArray(data) && data.length === 0) || 
            (data.stock_in && data.stock_in.length === 0 && data.stock_out && data.stock_out.length === 0)) {
            showNoData();
            return;
        }

        if (type === 'ingredient') {
            displayIngredientStatistics(data);
        } else if (type === 'ingredient_category') {
            displayIngredientCategoryStatistics(data);
        } else if (type === 'supplier') {
            displaySupplierStatistics(data);
        }
        
        showTable();
    }

    // 显示按分类汇总统计
    function displayIngredientCategoryStatistics(data) {
        const table = document.getElementById('statisticsTable');

        // 设置表头
        table.querySelector('thead').innerHTML = `
            <tr>
                <th>食材分类</th>
                <th>食材种类</th>
                <th>入库次数</th>
                <th>入库总量</th>
                <th>入库金额</th>
                <th>出库总量</th>
                <th>库存余量</th>
                <th>周转率</th>
                <th>成本占比</th>
                <th>平均单价</th>
            </tr>
        `;

        // 生成表格内容
        const tbody = table.querySelector('tbody');
        tbody.innerHTML = '';

        if (data && Array.isArray(data)) {
            data.forEach(item => {
                const row = `
                    <tr>
                        <td><strong><span class="badge badge-primary">${item.category_name || '其他'}</span></strong></td>
                        <td><span class="badge badge-info">${item.ingredient_count || 0}</span></td>
                        <td>${item.stock_in_count || 0}</td>
                        <td>${(parseFloat(item.total_in_quantity) || 0).toFixed(2)}</td>
                        <td class="text-success">¥${(parseFloat(item.total_in_amount) || 0).toFixed(2)}</td>
                        <td>${(parseFloat(item.total_out_quantity) || 0).toFixed(2)}</td>
                        <td class="${(parseFloat(item.net_quantity) || 0) >= 0 ? 'text-success' : 'text-warning'}">${(parseFloat(item.net_quantity) || 0).toFixed(2)}</td>
                        <td><span class="badge ${(parseFloat(item.turnover_rate) || 0) >= 80 ? 'badge-success' : (parseFloat(item.turnover_rate) || 0) >= 50 ? 'badge-warning' : 'badge-danger'}">${parseFloat(item.turnover_rate) || 0}%</span></td>
                        <td><span class="badge badge-secondary">${(parseFloat(item.cost_ratio) || 0).toFixed(1)}%</span></td>
                        <td>¥${(parseFloat(item.avg_unit_price) || 0).toFixed(2)}</td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }
    }

    // 显示按食材统计
    function displayIngredientStatistics(data) {
        const table = document.getElementById('statisticsTable');
        
        // 设置表头
        table.querySelector('thead').innerHTML = `
            <tr>
                <th>食材名称</th>
                <th>分类</th>
                <th>入库次数</th>
                <th>入库总量</th>
                <th>入库金额</th>
                <th>出库总量</th>
                <th>出库金额</th>
                <th>库存余量</th>
                <th>周转率</th>
                <th>平均单价</th>
            </tr>
        `;
        
        // 生成表格内容
        const tbody = table.querySelector('tbody');
        tbody.innerHTML = '';

        if (data && Array.isArray(data)) {
            data.forEach(item => {
                const row = `
                    <tr>
                        <td><strong>${item.ingredient_name || ''}</strong></td>
                        <td><span class="badge badge-secondary">${item.category_name || ''}</span></td>
                        <td>${item.stock_in_count || 0}</td>
                        <td>${(parseFloat(item.total_in_quantity) || 0).toFixed(2)}</td>
                        <td class="text-success">¥${(parseFloat(item.total_in_amount) || 0).toFixed(2)}</td>
                        <td>${(parseFloat(item.total_out_quantity) || 0).toFixed(2)}</td>
                        <td class="text-danger">¥${(parseFloat(item.total_out_amount) || 0).toFixed(2)}</td>
                        <td class="${(parseFloat(item.net_quantity) || 0) >= 0 ? 'text-success' : 'text-warning'}">${(parseFloat(item.net_quantity) || 0).toFixed(2)}</td>
                        <td><span class="badge ${(parseFloat(item.turnover_rate) || 0) >= 80 ? 'badge-success' : (parseFloat(item.turnover_rate) || 0) >= 50 ? 'badge-warning' : 'badge-danger'}">${parseFloat(item.turnover_rate) || 0}%</span></td>
                        <td>¥${(parseFloat(item.avg_unit_price) || 0).toFixed(2)}</td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }
    }

    // 显示按供应商统计 - 增强版本
    function displaySupplierStatistics(data) {
        const table = document.getElementById('statisticsTable');

        // 设置表头
        table.querySelector('thead').innerHTML = `
            <tr>
                <th>供应商信息</th>
                <th>供应汇总</th>
                <th>主要食材分类</th>
                <th>TOP5食材</th>
                <th>操作</th>
            </tr>
        `;

        // 生成表格内容
        const tbody = table.querySelector('tbody');
        tbody.innerHTML = '';

        if (data && Array.isArray(data)) {
            data.forEach(item => {
                // 构建食材分类信息
                let categoriesHtml = '';
                if (item.categories && Object.keys(item.categories).length > 0) {
                    Object.values(item.categories).forEach(category => {
                        categoriesHtml += `
                            <div class="mb-1">
                                <span class="badge badge-primary">${category.category_name}</span>
                                <small class="text-muted">
                                    ${category.ingredient_count}种 |
                                    ¥${category.total_amount.toFixed(0)}
                                    (${category.amount_ratio}%)
                                </small>
                            </div>
                        `;
                    });
                } else {
                    categoriesHtml = '<span class="text-muted">暂无分类数据</span>';
                }

                // 构建TOP5食材信息
                let topIngredientsHtml = '';
                if (item.top_ingredients && item.top_ingredients.length > 0) {
                    item.top_ingredients.forEach((ingredient, index) => {
                        topIngredientsHtml += `
                            <div class="mb-1">
                                <span class="badge badge-secondary">${index + 1}</span>
                                <strong>${ingredient.ingredient_name}</strong>
                                <small class="text-muted">
                                    (${ingredient.category_name})
                                    ¥${ingredient.total_amount.toFixed(0)}
                                </small>
                            </div>
                        `;
                    });
                } else {
                    topIngredientsHtml = '<span class="text-muted">暂无食材数据</span>';
                }

                const row = `
                    <tr>
                        <td>
                            <div><strong>${item.supplier_name || ''}</strong></div>
                            <div class="text-muted small">
                                ${item.contact_person ? '联系人: ' + item.contact_person : ''}
                                ${item.phone ? ' | 电话: ' + item.phone : ''}
                            </div>
                        </td>
                        <td>
                            <div><span class="badge badge-success">供应次数: ${item.stock_in_count || 0}</span></div>
                            <div><span class="badge badge-info">食材种类: ${item.ingredient_types || 0}</span></div>
                            <div class="text-success"><strong>总金额: ¥${(parseFloat(item.total_amount) || 0).toFixed(2)}</strong></div>
                            <div class="text-muted small">总量: ${(parseFloat(item.total_quantity) || 0).toFixed(2)} | 均价: ¥${(parseFloat(item.avg_unit_price) || 0).toFixed(2)}</div>
                        </td>
                        <td>
                            <div style="max-height: 150px; overflow-y: auto;">
                                ${categoriesHtml}
                            </div>
                        </td>
                        <td>
                            <div style="max-height: 150px; overflow-y: auto;">
                                ${topIngredientsHtml}
                            </div>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="showSupplierDetail(${item.supplier_id})">
                                <i class="fas fa-eye"></i> 详情
                            </button>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }
    }

    // 显示供应商详细信息
    function showSupplierDetail(supplierId) {
        // 找到对应的供应商数据
        const supplier = currentData.find(item => item.supplier_id === supplierId);
        if (!supplier) return;

        // 构建详细信息HTML
        let detailHtml = `
            <div class="supplier-detail">
                <h5>${supplier.supplier_name} - 详细分析</h5>

                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <ul class="list-unstyled">
                            <li><strong>联系人:</strong> ${supplier.contact_person || '未填写'}</li>
                            <li><strong>电话:</strong> ${supplier.phone || '未填写'}</li>
                            <li><strong>地址:</strong> ${supplier.address || '未填写'}</li>
                        </ul>

                        <h6>供应汇总</h6>
                        <ul class="list-unstyled">
                            <li><strong>供应次数:</strong> ${supplier.stock_in_count}</li>
                            <li><strong>食材种类:</strong> ${supplier.ingredient_types}</li>
                            <li><strong>供应总量:</strong> ${supplier.total_quantity.toFixed(2)}</li>
                            <li><strong>供应总金额:</strong> ¥${supplier.total_amount.toFixed(2)}</li>
                            <li><strong>平均单价:</strong> ¥${supplier.avg_unit_price.toFixed(2)}</li>
                        </ul>
                    </div>

                    <div class="col-md-6">
                        <h6>食材分类分布</h6>
                        <div class="mb-3">
        `;

        if (supplier.categories && Object.keys(supplier.categories).length > 0) {
            Object.values(supplier.categories).forEach(category => {
                detailHtml += `
                    <div class="mb-2 p-2 border rounded">
                        <div><strong>${category.category_name}</strong>
                            <span class="badge badge-primary">${category.amount_ratio}%</span>
                        </div>
                        <div class="small text-muted">
                            ${category.ingredient_count}种食材 |
                            ${category.stock_in_count}次供应 |
                            ¥${category.total_amount.toFixed(2)}
                        </div>
                    </div>
                `;
            });
        }

        detailHtml += `
                        </div>

                        <h6>主要供应食材</h6>
                        <div>
        `;

        if (supplier.top_ingredients && supplier.top_ingredients.length > 0) {
            supplier.top_ingredients.forEach((ingredient, index) => {
                detailHtml += `
                    <div class="mb-2 p-2 border rounded">
                        <div><span class="badge badge-secondary">${index + 1}</span>
                            <strong>${ingredient.ingredient_name}</strong>
                            <span class="badge badge-info">${ingredient.category_name}</span>
                        </div>
                        <div class="small text-muted">
                            ${ingredient.stock_in_count}次供应 |
                            ${ingredient.total_quantity.toFixed(2)}总量 |
                            ¥${ingredient.total_amount.toFixed(2)} |
                            均价¥${ingredient.avg_price.toFixed(2)}
                        </div>
                    </div>
                `;
            });
        }

        detailHtml += `
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 显示模态框
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">供应商详细分析</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        ${detailHtml}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        $(modal).modal('show');

        // 模态框关闭后移除
        $(modal).on('hidden.bs.modal', function() {
            document.body.removeChild(modal);
        });
    }

    // 显示欢迎界面
    function showWelcome() {
        document.getElementById('welcomeDiv').style.display = 'block';
        document.getElementById('loadingDiv').style.display = 'none';
        document.getElementById('tableContainer').style.display = 'none';
        document.getElementById('chartContainer').style.display = 'none';
        document.getElementById('noDataDiv').style.display = 'none';
    }

    // 显示加载状态
    function showLoading() {
        document.getElementById('welcomeDiv').style.display = 'none';
        document.getElementById('loadingDiv').style.display = 'block';
        document.getElementById('tableContainer').style.display = 'none';
        document.getElementById('chartContainer').style.display = 'none';
        document.getElementById('noDataDiv').style.display = 'none';
    }

    // 隐藏加载状态
    function hideLoading() {
        document.getElementById('loadingDiv').style.display = 'none';
    }

    // 显示表格
    function showTable() {
        document.getElementById('welcomeDiv').style.display = 'none';
        document.getElementById('tableContainer').style.display = 'block';
        document.getElementById('noDataDiv').style.display = 'none';
    }

    // 显示无数据
    function showNoData() {
        document.getElementById('welcomeDiv').style.display = 'none';
        document.getElementById('noDataDiv').style.display = 'block';
        document.getElementById('tableContainer').style.display = 'none';
        document.getElementById('chartContainer').style.display = 'none';
    }

    // 显示错误
    function showError(message) {
        // 使用更友好的错误提示
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            <strong>查询失败：</strong>${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;

        // 插入到表格容器前面
        const cardBody = document.querySelector('.card-body');
        cardBody.insertBefore(alertDiv, cardBody.firstChild);

        // 3秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }

    // 设置食材分类组
    function setCategoryGroup(groupName, clickedElement) {
        const categorySelect = document.getElementById('category_id');

        if (groupName === '') {
            // 选择全部分类
            categorySelect.value = '';
        } else {
            // 选择该组的第一个分类作为代表
            const group = categoryGroups[groupName];
            if (group && group.length > 0) {
                categorySelect.value = group[0].id;
            }
        }

        // 高亮选中的按钮
        document.querySelectorAll('#categoryQuickSelect .btn').forEach(btn => {
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-success', 'btn-outline-secondary');
        });

        // 如果有传递点击的元素，则高亮它
        if (clickedElement) {
            clickedElement.classList.remove('btn-outline-success', 'btn-outline-secondary');
            clickedElement.classList.add('btn-success');
        }

        // 自动查询
        loadStatistics();
    }

    // 打印统计报表
    function printStatistics() {
        const formData = new FormData(document.getElementById('filterForm'));
        const params = new URLSearchParams(formData);
        const printUrl = "{{ url_for('inventory.print_statistics') }}" + "?" + params.toString();
        window.open(printUrl, '_blank');
    }

    // 导出Excel（待实现）
    function exportData() {
        alert('Excel导出功能开发中...');
    }

    // 重置筛选条件
    function resetFilters() {
        document.getElementById('stat_type').value = 'ingredient';
        document.getElementById('storage_location_id').value = '';
        document.getElementById('category_id').value = '';
        document.getElementById('supplier_id').value = '';

        // 重置为最近30天
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30);

        document.getElementById('start_date').value = startDate.toISOString().split('T')[0];
        document.getElementById('end_date').value = endDate.toISOString().split('T')[0];

        // 重新加载统计
        loadStatistics();
    }

    // 设置日期范围
    function setDateRange(days) {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);

        document.getElementById('start_date').value = startDate.toISOString().split('T')[0];
        document.getElementById('end_date').value = endDate.toISOString().split('T')[0];

        // 自动查询
        loadStatistics();
    }

    // 设置本月
    function setCurrentMonth() {
        const now = new Date();
        const startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);

        document.getElementById('start_date').value = startDate.toISOString().split('T')[0];
        document.getElementById('end_date').value = endDate.toISOString().split('T')[0];

        // 自动查询
        loadStatistics();
    }

    // 设置上月
    function setLastMonth() {
        const now = new Date();
        const startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const endDate = new Date(now.getFullYear(), now.getMonth(), 0);

        document.getElementById('start_date').value = startDate.toISOString().split('T')[0];
        document.getElementById('end_date').value = endDate.toISOString().split('T')[0];

        // 自动查询
        loadStatistics();
    }
</script>
{% endblock %}
