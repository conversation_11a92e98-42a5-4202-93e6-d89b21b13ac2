{% extends 'base.html' %}

{% block title %}控制面板 - {{ super() }}{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- 仪表板头部 -->
    <div class="dashboard-header">
        <h1 class="dashboard-title">控制面板</h1>
        <p class="dashboard-subtitle">欢迎回来，{{ current_user.real_name or current_user.username }} | 今日 {{ moment().format('YYYY年MM月DD日 dddd') }}</p>
    </div>

    <!-- 统计卡片行 -->
    <div class="row">
        <div class="col-lg-3 col-md-6 col-12">
            <div class="stats-card primary">
                <div class="stats-card-header">
                    <div>
                        <h6 class="stats-card-title">供应商</h6>
                        <div class="stats-card-value">{{ suppliers_count }}</div>
                        <p class="stats-card-label">合作供应商总数</p>
                    </div>
                    <div class="stats-card-icon">
                        <i class="fas fa-building"></i>
                    </div>
                </div>
                <div class="stats-card-footer">
                    <div class="stats-card-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>本月新增 3 家</span>
                    </div>
                    <a href="{{ url_for('main.suppliers') }}" class="stats-card-link">
                        查看详情 <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
            <div class="stats-card success">
                <div class="stats-card-header">
                    <div>
                        <h6 class="stats-card-title">食材</h6>
                        <div class="stats-card-value">{{ ingredients_count }}</div>
                        <p class="stats-card-label">食材品种总数</p>
                    </div>
                    <div class="stats-card-icon">
                        <i class="fas fa-carrot"></i>
                    </div>
                </div>
                <div class="stats-card-footer">
                    <div class="stats-card-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>本周新增 12 种</span>
                    </div>
                    <a href="{{ url_for('main.ingredients') }}" class="stats-card-link">
                        查看详情 <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
            <div class="stats-card warning">
                <div class="stats-card-header">
                    <div>
                        <h6 class="stats-card-title">食谱</h6>
                        <div class="stats-card-value">{{ recipes_count }}</div>
                        <p class="stats-card-label">标准食谱总数</p>
                    </div>
                    <div class="stats-card-icon">
                        <i class="fas fa-utensils"></i>
                    </div>
                </div>
                <div class="stats-card-footer">
                    <div class="stats-card-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>本月新增 8 个</span>
                    </div>
                    <a href="{{ url_for('main.recipes') }}" class="stats-card-link">
                        查看详情 <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-12">
            <div class="stats-card danger">
                <div class="stats-card-header">
                    <div>
                        <h6 class="stats-card-title">留样</h6>
                        <div class="stats-card-value">{{ samples_count }}</div>
                        <p class="stats-card-label">食品留样记录</p>
                    </div>
                    <div class="stats-card-icon">
                        <i class="fas fa-vial"></i>
                    </div>
                </div>
                <div class="stats-card-footer">
                    <div class="stats-card-change positive">
                        <i class="fas fa-check"></i>
                        <span>今日已完成</span>
                    </div>
                    <a href="{{ url_for('main.food_samples') }}" class="stats-card-link">
                        查看详情 <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="row">
        <div class="col-lg-8 col-12">
            <!-- 最近采购订单 -->
            <div class="chart-card">
                <div class="chart-card-header">
                    <h5 class="chart-card-title">最近采购订单</h5>
                    <p class="chart-card-subtitle">最新的采购订单状态和详情</p>
                </div>
                <div class="chart-card-body">
                    <!-- 桌面端企业级表格 -->
                    <div class="enterprise-table-container d-none d-md-block">
                        <table class="enterprise-table">
                            <thead>
                                <tr>
                                    <th class="sortable">订单号</th>
                                    <th class="sortable">供应商</th>
                                    <th class="amount-col sortable">金额</th>
                                    <th class="status-col">状态</th>
                                    <th class="date-col sortable">日期</th>
                                    <th class="action-col">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in recent_orders %}
                                <tr>
                                    <td class="number-col">#{{ order.id }}</td>
                                    <td>
                                        <strong>{{ order.supplier.name }}</strong>
                                        {% if order.supplier.rating %}
                                        <div class="text-warning small">
                                            {% for i in range(order.supplier.rating|int) %}
                                            <i class="fas fa-star"></i>
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td class="amount-col">{{ "%.2f"|format(order.total_amount) }}</td>
                                    <td class="status-col">
                                        {% if order.status == '待审核' %}
                                        <span class="badge badge-warning">{{ order.status }}</span>
                                        {% elif order.status == '已发货' %}
                                        <span class="badge badge-info">{{ order.status }}</span>
                                        {% elif order.status == '已完成' %}
                                        <span class="badge badge-success">{{ order.status }}</span>
                                        {% else %}
                                        <span class="badge badge-secondary">{{ order.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td class="date-col">{{ order.order_date|format_datetime('%Y-%m-%d') }}</td>
                                    <td class="action-col">
                                        <div class="btn-group btn-group-sm">
                                            <a href="#" class="btn btn-outline-primary btn-xs" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="#" class="btn btn-outline-success btn-xs" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="6" class="table-empty">
                                        <div class="table-empty-icon">
                                            <i class="fas fa-shopping-cart"></i>
                                        </div>
                                        <div class="table-empty-text">暂无采购订单</div>
                                        <div class="table-empty-subtext">您可以创建新的采购订单</div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 移动端卡片保持原样 -->
                    <div class="d-md-none">
                        {% for order in recent_orders %}
                        <div class="card mb-2 border-left-primary">
                            <div class="card-body py-2">
                                <div class="row">
                                    <div class="col-8">
                                        <h6 class="mb-1">订单 #{{ order.id }}</h6>
                                        <small class="text-muted">{{ order.supplier.name }}</small>
                                    </div>
                                    <div class="col-4 text-right">
                                        <div class="font-weight-bold">¥{{ order.total_amount }}</div>
                                        {% if order.status == '待审核' %}
                                        <span class="badge badge-warning">{{ order.status }}</span>
                                        {% elif order.status == '已发货' %}
                                        <span class="badge badge-info">{{ order.status }}</span>
                                        {% elif order.status == '已完成' %}
                                        <span class="badge badge-success">{{ order.status }}</span>
                                        {% else %}
                                        <span class="badge badge-secondary">{{ order.status }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="row mt-1">
                                    <div class="col-12">
                                        <small class="text-muted">{{ order.order_date|format_datetime('%Y-%m-%d') }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <p class="text-muted">暂无采购订单</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                <div class="table-pagination">
                    <div class="pagination-info">
                        显示最近 {{ recent_orders|length }} 条订单
                    </div>
                    <a href="{{ url_for('main.purchase_orders') }}" class="btn btn-primary btn-sm">
                        查看所有订单 <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-12">
            <!-- 快捷操作面板 -->
            <div class="quick-actions">
                <div class="quick-actions-header">
                    <h5 class="quick-actions-title">快捷操作</h5>
                </div>
                <div class="quick-actions-body">
                    <a href="{{ url_for('supplier.create') }}" class="quick-action-item">
                        <div class="quick-action-icon" style="background: #2c5aa0;">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="quick-action-content">
                            <div class="quick-action-title">添加供应商</div>
                            <div class="quick-action-desc">新增合作供应商信息</div>
                        </div>
                        <div class="quick-action-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>

                    <a href="{{ url_for('ingredient.create') }}" class="quick-action-item">
                        <div class="quick-action-icon" style="background: #198754;">
                            <i class="fas fa-carrot"></i>
                        </div>
                        <div class="quick-action-content">
                            <div class="quick-action-title">添加食材</div>
                            <div class="quick-action-desc">录入新的食材品种</div>
                        </div>
                        <div class="quick-action-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>

                    <a href="{{ url_for('recipe.create') }}" class="quick-action-item">
                        <div class="quick-action-icon" style="background: #ffc107; color: #333;">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="quick-action-content">
                            <div class="quick-action-title">添加食谱</div>
                            <div class="quick-action-desc">创建标准化食谱</div>
                        </div>
                        <div class="quick-action-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>

                    <a href="{{ url_for('purchase_order.create') }}" class="quick-action-item">
                        <div class="quick-action-icon" style="background: #0dcaf0; color: #333;">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="quick-action-content">
                            <div class="quick-action-title">创建采购订单</div>
                            <div class="quick-action-desc">生成新的采购计划</div>
                        </div>
                        <div class="quick-action-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>

                    <a href="{{ url_for('food_sample.create') }}" class="quick-action-item">
                        <div class="quick-action-icon" style="background: #dc3545;">
                            <i class="fas fa-vial"></i>
                        </div>
                        <div class="quick-action-content">
                            <div class="quick-action-title">添加留样记录</div>
                            <div class="quick-action-desc">记录食品安全留样</div>
                        </div>
                        <div class="quick-action-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
