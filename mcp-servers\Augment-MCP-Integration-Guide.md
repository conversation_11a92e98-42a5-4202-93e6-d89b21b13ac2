# Augment MCP 集成指南 - StudentsCMSSP开发工具集

## 🎯 概述

本指南将帮助您将StudentsCMSSP开发辅助MCP工具集成到Augment设置中，提供强大的AI辅助开发能力。

## 📦 已安装的MCP服务器

### 1. **SQL Server MCP** 
- **ID**: `studentscms-sqlserver`
- **功能**: StudentsCMSSP数据库查询和分析
- **工具**: `query_database`, `list_tables`, `describe_table`, `get_table_data`

### 2. **Filesystem MCP**
- **ID**: `studentscms-filesystem` 
- **功能**: 安全的文件系统操作（限制在项目目录内）
- **工具**: `read_file`, `write_file`, `list_directory`, `create_directory`

### 3. **Excel MCP**
- **ID**: `studentscms-excel`
- **功能**: Excel文件处理和财务报表生成
- **工具**: `read_excel`, `write_excel`, `get_sheet_names`, `create_financial_report`

### 4. **Pandoc MCP**
- **ID**: `studentscms-pandoc`
- **功能**: 文档格式转换和模板生成
- **工具**: `convert_document`, `markdown_to_pdf`, `html_to_pdf`, `create_financial_doc`

### 5. **Layout Optimizer MCP**
- **ID**: `studentscms-layout-optimizer`
- **功能**: 网页布局优化和响应式测试
- **工具**: `analyze_layout`, `responsive_test`, `studentscms_layout_check`, `check_mobile_layout`

## 🔧 Augment 集成步骤

### 方法1: 使用Augment设置界面

1. **打开Augment设置**
   - 进入 `TOOLS` > `MCP`

2. **添加MCP服务器**
   - 点击 "Add MCP Server" 或类似按钮

3. **逐个配置每个服务器**：

#### SQL Server MCP 配置
```json
{
  "name": "StudentsCMSSP SQL Server",
  "command": "node",
  "args": ["C:\\StudentsCMSSP\\mcp-sqlserver\\server.js"],
  "env": {
    "MSSQL_SERVER": "14.103.246.164",
    "MSSQL_DATABASE": "StudentsCMSSP", 
    "MSSQL_USER": "StudentsCMSSP",
    "MSSQL_PASSWORD": "Xg2LS44Cyz5Zt8."
  }
}
```

#### Filesystem MCP 配置
```json
{
  "name": "StudentsCMSSP Filesystem",
  "command": "node",
  "args": ["C:\\StudentsCMSSP\\mcp-servers\\filesystem-mcp\\server.js"]
}
```

#### Excel MCP 配置
```json
{
  "name": "StudentsCMSSP Excel",
  "command": "node", 
  "args": ["C:\\StudentsCMSSP\\mcp-servers\\excel-mcp\\server.js"]
}
```

#### Pandoc MCP 配置
```json
{
  "name": "StudentsCMSSP Pandoc",
  "command": "node",
  "args": ["C:\\StudentsCMSSP\\mcp-servers\\pandoc-mcp\\server.js"]
}
```

#### Layout Optimizer MCP 配置
```json
{
  "name": "StudentsCMSSP Layout Optimizer", 
  "command": "node",
  "args": ["C:\\StudentsCMSSP\\mcp-servers\\layout-optimizer-mcp\\server.js"]
}
```

### 方法2: 使用配置文件

如果Augment支持配置文件导入，可以使用：

1. **复制配置文件**
   ```
   mcp-servers/augment-mcp-config.json
   ```

2. **导入到Augment设置**
   - 在Augment设置中查找"导入配置"或"Import Configuration"选项
   - 选择 `augment-mcp-config.json` 文件

### 方法3: 手动配置

如果需要手动输入，请使用以下信息：

| 服务器名称 | 命令 | 参数 | 工作目录 |
|------------|------|------|----------|
| StudentsCMSSP SQL Server | node | server.js | C:\StudentsCMSSP\mcp-sqlserver |
| StudentsCMSSP Filesystem | node | server.js | C:\StudentsCMSSP\mcp-servers\filesystem-mcp |
| StudentsCMSSP Excel | node | server.js | C:\StudentsCMSSP\mcp-servers\excel-mcp |
| StudentsCMSSP Pandoc | node | server.js | C:\StudentsCMSSP\mcp-servers\pandoc-mcp |
| StudentsCMSSP Layout Optimizer | node | server.js | C:\StudentsCMSSP\mcp-servers\layout-optimizer-mcp |

## ✅ 验证集成

集成完成后，您应该能够在Augment中使用以下工具：

### 数据库操作
- 查询StudentsCMSSP数据库表结构
- 分析业务数据关系
- 获取示例数据

### 文件操作  
- 读取项目文件
- 创建新文件和目录
- 安全的文件系统访问

### Excel处理
- 生成财务报表模板
- 处理Excel数据文件
- 创建数据分析报告

### 文档转换
- 将Markdown转换为PDF
- 生成财务文档模板
- 多格式文档处理

### 布局优化
- 分析网页布局问题
- 测试响应式设计
- 移动端适配检查

## 🎯 使用示例

集成成功后，您可以这样使用：

```
"请使用SQL Server MCP查询StudentsCMSSP数据库中的所有表"
"使用Filesystem MCP读取app/models.py文件"
"用Excel MCP创建一个资产负债表模板"
"使用Layout Optimizer检查财务模块的移动端布局"
```

## 🔧 故障排除

### 常见问题

1. **路径问题**: 确保所有路径使用绝对路径
2. **权限问题**: 确保Node.js有执行权限
3. **依赖问题**: 确保所有npm包已正确安装
4. **端口冲突**: 确保没有其他服务占用相同端口

### 检查命令
```bash
# 检查Node.js版本
node --version

# 测试MCP服务器
cd C:\StudentsCMSSP\mcp-sqlserver
node server.js

# 检查依赖安装
npm list
```

## 🎊 完成！

集成完成后，您将拥有一个强大的AI辅助开发环境，专门为StudentsCMSSP项目优化，包含数据库访问、文件操作、Excel处理、文档转换和布局优化等全套开发工具！
