# StudentsCMSSP 开发辅助 MCP 服务器

## 🎯 目的
这些MCP服务器专门为AI辅助开发StudentsCMSSP项目而设计，提供强大的开发支持功能。

## 📦 已安装的MCP服务器

### 1. SQL Server MCP 服务器
- **位置**: `../mcp-sqlserver/`
- **功能**: 
  - 查询StudentsCMSSP数据库
  - 获取表结构信息
  - 分析数据关系
- **工具**:
  - `query_database` - 执行SQL查询
  - `list_tables` - 列出所有表
  - `describe_table` - 获取表结构
  - `get_table_data` - 获取表数据样本

### 2. Filesystem MCP 服务器
- **位置**: `./filesystem-mcp/`
- **功能**: 
  - 安全的文件系统操作
  - 限制在StudentsCMSSP项目目录内
  - 文件读写和目录管理
- **工具**:
  - `read_file` - 读取文件内容
  - `write_file` - 写入文件内容
  - `list_directory` - 列出目录内容
  - `create_directory` - 创建目录

### 3. Excel MCP 服务器
- **位置**: `./excel-mcp/`
- **功能**: 
  - Excel文件读写
  - 财务报表模板生成
  - 数据分析支持
- **工具**:
  - `read_excel` - 读取Excel文件
  - `write_excel` - 写入Excel文件
  - `get_sheet_names` - 获取工作表名称
  - `create_financial_report` - 创建财务报表模板

### 4. Pandoc MCP 服务器
- **位置**: `./pandoc-mcp/`
- **功能**:
  - 文档格式转换
  - 财务文档模板生成
  - PDF/Word/HTML转换
- **工具**:
  - `convert_document` - 通用文档转换
  - `markdown_to_pdf` - Markdown转PDF
  - `html_to_pdf` - HTML转PDF
  - `create_financial_doc` - 创建财务文档模板

### 5. Layout Optimizer MCP 服务器 ⭐⭐⭐⭐⭐
- **位置**: `./layout-optimizer-mcp/`
- **功能**:
  - 网页布局分析和优化
  - 响应式设计测试
  - 移动端适配检查
  - StudentsCMSSP专用布局优化
- **工具**:
  - `analyze_layout` - 分析网页布局并提供优化建议
  - `responsive_test` - 测试响应式设计
  - `studentscms_layout_check` - StudentsCMSSP页面专用布局检查
  - `check_mobile_layout` - 移动端布局优化检查

## 🚀 快速启动

### 启动所有服务器
```bash
start-all-servers.bat
```

### 单独启动服务器
```bash
# SQL Server MCP
cd ../mcp-sqlserver && node server.js

# Filesystem MCP
cd filesystem-mcp && node server.js

# Excel MCP
cd excel-mcp && node server.js

# Pandoc MCP
cd pandoc-mcp && node server.js
```

## 💡 使用场景

### 开发场景
1. **代码分析**: 使用Filesystem MCP读取项目文件
2. **数据库查询**: 使用SQL Server MCP分析数据结构
3. **文档生成**: 使用Pandoc MCP创建技术文档
4. **数据处理**: 使用Excel MCP处理测试数据

### 财务模块开发
1. **报表模板**: 使用Excel MCP创建财务报表模板
2. **文档转换**: 使用Pandoc MCP生成PDF报表
3. **数据验证**: 使用SQL Server MCP验证财务数据
4. **文件管理**: 使用Filesystem MCP管理财务文档

## 🔧 配置说明

### 安全限制
- Filesystem MCP限制在StudentsCMSSP项目目录内
- SQL Server MCP只允许SELECT查询
- 所有服务器都有错误处理和日志记录

### 依赖要求
- Node.js 18+
- npm 包管理器
- Pandoc (用于文档转换)
- wkhtmltopdf (用于PDF生成)

## 📝 开发建议

1. **使用Filesystem MCP**查看项目结构
2. **使用SQL Server MCP**了解数据模型
3. **使用Excel MCP**处理财务数据
4. **使用Pandoc MCP**生成项目文档

## 🎯 AI开发支持

这些MCP服务器让AI能够：
- 深入理解项目架构
- 安全地操作文件系统
- 查询和分析数据库
- 处理Excel财务数据
- 生成各种格式的文档

通过这些工具，AI可以更好地辅助StudentsCMSSP项目的开发工作！
