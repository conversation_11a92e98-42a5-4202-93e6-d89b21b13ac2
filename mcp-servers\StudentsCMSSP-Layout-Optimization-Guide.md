# StudentsCMSSP 网页布局优化指南

## 🎯 火山引擎MCP市场中的布局优化服务

### ✅ **推荐的网页布局优化MCP服务**

#### 1. **Browser-Use MCP** ⭐⭐⭐⭐⭐
```
来源: volcengine/mcp-server/tree/main/server/mcp_server_vefaas_browser_use
```
- **功能**: 基于Browser-use的浏览器自动化工具
- **布局优化用途**:
  - 自动化网页测试和布局检查
  - 截图对比和视觉回归测试
  - 响应式设计测试
  - 跨浏览器兼容性检查

#### 2. **Figma MCP** ⭐⭐⭐⭐⭐
```
来源: GLips/Figma-Context-MCP
```
- **功能**: 为AI提供Figma文件的布局和样式信息
- **布局优化用途**:
  - 读取设计稿布局信息
  - 获取设计规范和样式
  - 自动生成CSS代码
  - 设计与开发的一致性检查

#### 3. **Puppeteer MCP** ⭐⭐⭐⭐
```
来源: modelcontextprotocol/servers/tree/main/src/puppeteer
```
- **功能**: 浏览器自动化和网页抓取
- **布局优化用途**:
  - 自动化布局测试
  - 性能分析
  - 移动端适配测试

#### 4. **Layout Optimizer MCP** ⭐⭐⭐⭐⭐ (本地安装)
```
位置: ./layout-optimizer-mcp/
```
- **功能**: 专为StudentsCMSSP定制的布局优化工具
- **特色功能**:
  - StudentsCMSSP页面专用布局检查
  - 财务模块布局优化
  - 移动端适配检查
  - 响应式设计测试

## 🎨 StudentsCMSSP 布局优化重点

### 财务模块布局优化
- **表格对齐**: 确保金额右对齐，科目左对齐
- **字体大小**: 使用13px基础字体，12px用于标签
- **颜色方案**: 使用yonyou-theme.css配色
- **响应式表格**: 在移动端使用水平滚动

### 移动端优化重点
- **触摸目标**: 最小44x44px的按钮尺寸
- **字体大小**: 移动端最小14px字体
- **导航菜单**: 可折叠的导航设计
- **表单优化**: 适合移动端输入的表单布局

### 用户界面一致性
- **统一间距**: 使用一致的边距和内边距
- **按钮样式**: 统一的按钮设计和状态
- **色彩对比**: 确保足够的对比度
- **加载状态**: 清晰的加载和状态指示

## 🚀 使用Layout Optimizer MCP

### 基本用法
```javascript
// 分析页面布局
analyze_layout({
  url: "http://localhost:8080/financial/vouchers",
  viewport: { width: 1920, height: 1080 }
})

// 响应式测试
responsive_test({
  url: "http://localhost:8080/inventory/stock-in",
  devices: ["mobile", "tablet", "desktop"]
})

// StudentsCMSSP专用检查
studentscms_layout_check({
  page_type: "financial",
  url: "http://localhost:8080/financial/reports"
})

// 移动端布局检查
check_mobile_layout({
  url: "http://localhost:8080/dashboard"
})
```

### 针对不同模块的优化建议

#### 财务模块
- 使用固定表头的表格设计
- 金额字段右对齐并使用等宽字体
- 凭证编辑界面采用用友风格布局
- 确保打印样式的优化

#### 库存管理
- 数据表格支持排序和筛选
- 批量操作按钮的合理布局
- 库存状态的视觉化显示
- 移动端的简化视图

#### 菜单管理
- 图片和文字的合理比例
- 拖拽排序的用户体验
- 营养信息的清晰展示
- 季节性菜单的标识

#### 仪表板
- 卡片式布局的响应式设计
- 图表的自适应尺寸
- 关键指标的突出显示
- 快捷操作的便捷访问

## 🔧 安装和配置

### 安装Layout Optimizer MCP
```bash
cd mcp-servers/layout-optimizer-mcp
npm install
npx playwright install
```

### 启动服务
```bash
node server.js
```

### 集成到开发流程
1. 在开发新功能时使用布局分析
2. 在发布前进行响应式测试
3. 定期检查移动端适配
4. 使用自动化测试验证布局一致性

## 📊 布局优化检查清单

### 桌面端检查
- [ ] 页面在1920x1080分辨率下正常显示
- [ ] 表格列宽合理，内容不溢出
- [ ] 按钮和链接有合适的间距
- [ ] 字体大小符合13px标准

### 移动端检查
- [ ] 页面在375px宽度下正常显示
- [ ] 触摸目标大小至少44x44px
- [ ] 文字大小至少14px
- [ ] 导航菜单可正常使用

### 可访问性检查
- [ ] 颜色对比度符合WCAG标准
- [ ] 键盘导航功能正常
- [ ] 屏幕阅读器兼容性
- [ ] 表单标签正确关联

### 性能检查
- [ ] 页面加载时间合理
- [ ] 图片优化和懒加载
- [ ] CSS和JS文件压缩
- [ ] 缓存策略合理

通过使用这些MCP服务和优化指南，可以显著提升StudentsCMSSP项目的用户界面质量和用户体验！
