@echo off
echo ========================================
echo StudentsCMSSP MCP工具集 - Augment集成设置
echo ========================================
echo.

echo 正在检查MCP服务器状态...
echo.

REM 检查各个MCP服务器文件是否存在
set "all_ready=true"

if not exist "..\mcp-sqlserver\server.js" (
    echo ✗ SQL Server MCP服务器文件不存在
    set "all_ready=false"
) else (
    echo ✓ SQL Server MCP服务器就绪
)

if not exist "filesystem-mcp\server.js" (
    echo ✗ Filesystem MCP服务器文件不存在
    set "all_ready=false"
) else (
    echo ✓ Filesystem MCP服务器就绪
)

if not exist "excel-mcp\server.js" (
    echo ✗ Excel MCP服务器文件不存在
    set "all_ready=false"
) else (
    echo ✓ Excel MCP服务器就绪
)

if not exist "pandoc-mcp\server.js" (
    echo ✗ Pandoc MCP服务器文件不存在
    set "all_ready=false"
) else (
    echo ✓ Pandoc MCP服务器就绪
)

if not exist "layout-optimizer-mcp\server.js" (
    echo ✗ Layout Optimizer MCP服务器文件不存在
    set "all_ready=false"
) else (
    echo ✓ Layout Optimizer MCP服务器就绪
)

echo.

if "%all_ready%"=="false" (
    echo 错误：部分MCP服务器未安装完成
    echo 请先运行安装脚本
    pause
    exit /b 1
)

echo 所有MCP服务器检查完成！
echo.

echo ========================================
echo Augment MCP 配置信息
echo ========================================
echo.

echo 请将以下配置添加到Augment设置中的TOOLS > MCP：
echo.

echo 1. SQL Server MCP:
echo    名称: StudentsCMSSP SQL Server
echo    命令: node
echo    参数: C:\StudentsCMSSP\mcp-sqlserver\server.js
echo    环境变量: 
echo      MSSQL_SERVER=14.103.246.164
echo      MSSQL_DATABASE=StudentsCMSSP
echo      MSSQL_USER=StudentsCMSSP
echo      MSSQL_PASSWORD=Xg2LS44Cyz5Zt8.
echo.

echo 2. Filesystem MCP:
echo    名称: StudentsCMSSP Filesystem
echo    命令: node
echo    参数: C:\StudentsCMSSP\mcp-servers\filesystem-mcp\server.js
echo.

echo 3. Excel MCP:
echo    名称: StudentsCMSSP Excel
echo    命令: node
echo    参数: C:\StudentsCMSSP\mcp-servers\excel-mcp\server.js
echo.

echo 4. Pandoc MCP:
echo    名称: StudentsCMSSP Pandoc
echo    命令: node
echo    参数: C:\StudentsCMSSP\mcp-servers\pandoc-mcp\server.js
echo.

echo 5. Layout Optimizer MCP:
echo    名称: StudentsCMSSP Layout Optimizer
echo    命令: node
echo    参数: C:\StudentsCMSSP\mcp-servers\layout-optimizer-mcp\server.js
echo.

echo ========================================
echo 配置文件位置
echo ========================================
echo.
echo Augment配置文件: augment-mcp-config.json
echo Claude Desktop配置文件: claude-desktop-config.json
echo 详细集成指南: Augment-MCP-Integration-Guide.md
echo.

echo 配置完成后，您将拥有以下AI开发工具：
echo - 数据库查询和分析
echo - 安全文件系统操作
echo - Excel文件处理和财务报表生成
echo - 文档格式转换
echo - 网页布局优化和响应式测试
echo.

echo ========================================
echo 下一步操作
echo ========================================
echo.
echo 1. 打开Augment设置 (TOOLS > MCP)
echo 2. 添加上述5个MCP服务器配置
echo 3. 保存设置并重启Augment
echo 4. 测试MCP工具是否正常工作
echo.

pause
