#!/usr/bin/env python3
"""
StudentsCMSSP 全模板高级改造脚本
自动化应用现代企业级设计到所有模板
"""

import os
import re
import glob
from pathlib import Path

class TemplateUpgrader:
    def __init__(self):
        self.template_dir = Path("app/templates")
        self.backup_dir = Path("backups/templates")
        self.changes_log = []
        
    def backup_templates(self):
        """备份所有模板文件"""
        print("📦 备份现有模板...")
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        for template_file in self.template_dir.rglob("*.html"):
            relative_path = template_file.relative_to(self.template_dir)
            backup_file = self.backup_dir / relative_path
            backup_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(content)
        
        print(f"✅ 已备份到 {self.backup_dir}")
    
    def fix_layout_containers(self):
        """修复所有模板的布局容器问题"""
        print("🔧 修复布局容器...")
        
        # 需要修复的模板文件
        template_files = list(self.template_dir.rglob("*.html"))
        
        for template_file in template_files:
            if template_file.name == "base.html":
                continue
                
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # 修复模式1: {% block content %} 后直接有 container-fluid
                pattern1 = r'(\{% block content %\}\s*\n)\s*<div class="container-fluid">\s*\n\s*<div class="row">\s*\n\s*<div class="col-md-12">\s*\n\s*(<div class="card">)'
                replacement1 = r'\1\2'
                content = re.sub(pattern1, replacement1, content, flags=re.MULTILINE)
                
                # 修复模式2: 结尾的容器闭合标签
                pattern2 = r'(\s*</div>\s*\n\s*</div>\s*\n\s*</div>\s*\n\s*</div>\s*\n)(\{% endblock %\})'
                replacement2 = r'                </div>\n\2'
                content = re.sub(pattern2, replacement2, content, flags=re.MULTILINE)
                
                # 如果有修改，保存文件
                if content != original_content:
                    with open(template_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.changes_log.append(f"✅ 修复布局: {template_file.relative_to(self.template_dir)}")
                    
            except Exception as e:
                print(f"❌ 处理 {template_file} 时出错: {e}")
    
    def upgrade_table_designs(self):
        """升级表格设计"""
        print("🎨 升级表格设计...")
        
        # 查找包含 enterprise-table 的模板
        table_templates = []
        
        for template_file in self.template_dir.rglob("*.html"):
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'enterprise-table' in content:
                    table_templates.append(template_file)
            except:
                continue
        
        for template_file in table_templates:
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # 升级表格容器类
                content = content.replace(
                    'class="enterprise-table-container"',
                    'class="enterprise-table-v2-container desktop-only"'
                )
                
                # 升级工具栏类
                content = content.replace(
                    'class="table-toolbar"',
                    'class="smart-toolbar"'
                )
                
                content = content.replace(
                    'class="table-toolbar-left"',
                    'class="toolbar-left"'
                )
                
                content = content.replace(
                    'class="table-toolbar-right"',
                    'class="toolbar-right"'
                )
                
                # 升级表格类
                content = content.replace(
                    'class="enterprise-table"',
                    'class="enterprise-table-v2"'
                )
                
                # 添加移动端卡片视图（如果不存在）
                if 'mobile-only' not in content and 'enterprise-table-v2' in content:
                    # 在表格容器后添加移动端视图
                    mobile_view = '''
                    
                    <!-- 移动端卡片视图 -->
                    <div class="mobile-only">
                        <div class="row">
                            {% for item in items %}
                            <div class="col-12 mb-3">
                                <div class="card">
                                    <div class="card-body">
                                        <!-- 移动端卡片内容 -->
                                        <h6 class="card-title">{{ item.name or item.title }}</h6>
                                        <p class="card-text">{{ item.description or item.summary }}</p>
                                        <div class="btn-group btn-group-sm">
                                            <a href="#" class="btn btn-outline-primary">查看</a>
                                            <a href="#" class="btn btn-outline-secondary">编辑</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>'''
                    
                    # 在表格分页后插入移动端视图
                    if '</div>\n                        {% endif %}' in content:
                        content = content.replace(
                            '</div>\n                        {% endif %}',
                            '</div>\n                        {% endif %}' + mobile_view
                        )
                
                if content != original_content:
                    with open(template_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.changes_log.append(f"🎨 升级表格: {template_file.relative_to(self.template_dir)}")
                    
            except Exception as e:
                print(f"❌ 升级表格 {template_file} 时出错: {e}")
    
    def add_modern_headers(self):
        """添加现代化页面头部"""
        print("✨ 添加现代化页面头部...")
        
        # 需要升级的主要模板
        target_templates = [
            "ingredient/index.html",
            "purchase_order/index.html", 
            "stock_in/index.html",
            "stock_out/index.html",
            "inventory/index.html",
            "financial/voucher/index.html"
        ]
        
        for template_name in target_templates:
            template_file = self.template_dir / template_name
            
            if not template_file.exists():
                continue
                
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # 查找并升级卡片头部
                header_pattern = r'<div class="card-header">\s*\n\s*<div class="d-flex justify-content-between align-items-center">\s*\n\s*<h3 class="card-title mb-0">([^<]+)</h3>'
                
                def header_replacement(match):
                    title = match.group(1).strip()
                    icon_map = {
                        '食材管理': 'fas fa-carrot',
                        '采购订单': 'fas fa-shopping-cart',
                        '入库管理': 'fas fa-warehouse',
                        '出库管理': 'fas fa-truck',
                        '库存管理': 'fas fa-boxes',
                        '财务凭证': 'fas fa-file-invoice'
                    }
                    
                    icon = icon_map.get(title, 'fas fa-list')
                    
                    return f'''<div class="card-header">
                    <!-- 桌面端布局 -->
                    <div class="d-flex justify-content-between align-items-center desktop-only">
                        <div>
                            <h3 class="card-title mb-0">
                                <i class="{icon} me-2"></i>{title}
                            </h3>
                            <small class="text-muted">专业的企业级管理界面</small>
                        </div>'''
                
                content = re.sub(header_pattern, header_replacement, content)
                
                if content != original_content:
                    with open(template_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    self.changes_log.append(f"✨ 现代化头部: {template_name}")
                    
            except Exception as e:
                print(f"❌ 升级头部 {template_file} 时出错: {e}")
    
    def generate_report(self):
        """生成改造报告"""
        print("\n📊 生成改造报告...")
        
        report_content = f"""# StudentsCMSSP 模板改造报告

## 改造时间
{os.popen('date').read().strip()}

## 改造统计
- 总计修改文件: {len(self.changes_log)} 个
- 备份位置: {self.backup_dir}

## 详细修改记录
"""
        
        for change in self.changes_log:
            report_content += f"- {change}\n"
        
        report_content += f"""

## 改造内容

### 1. 布局修复
- 删除重复的Bootstrap容器类
- 修复左侧导航栏间隙问题
- 统一布局结构

### 2. 表格升级
- 应用企业级表格设计V2
- 现代化工具栏
- 智能搜索集成
- 响应式移动端适配

### 3. 视觉优化
- 现代化页面头部
- 统一图标系统
- 优化状态徽章
- 改进操作按钮

### 4. 用户体验
- 快速搜索功能
- 表格排序优化
- 悬停效果增强
- 加载状态改进

## 下一步计划
1. 测试所有修改的页面
2. 收集用户反馈
3. 继续优化移动端体验
4. 添加更多交互功能
"""
        
        with open("docs/模板改造报告.md", 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"📄 报告已保存到: docs/模板改造报告.md")
    
    def run_upgrade(self):
        """执行完整的升级流程"""
        print("🚀 开始StudentsCMSSP模板高级改造...")
        print("=" * 50)
        
        # 1. 备份
        self.backup_templates()
        
        # 2. 修复布局
        self.fix_layout_containers()
        
        # 3. 升级表格
        self.upgrade_table_designs()
        
        # 4. 现代化头部
        self.add_modern_headers()
        
        # 5. 生成报告
        self.generate_report()
        
        print("\n🎉 模板改造完成!")
        print(f"📊 总计修改: {len(self.changes_log)} 个文件")
        print("📄 详细报告: docs/模板改造报告.md")
        print("\n💡 建议:")
        print("1. 重启开发服务器")
        print("2. 测试所有修改的页面")
        print("3. 检查移动端显示效果")

if __name__ == "__main__":
    upgrader = TemplateUpgrader()
    upgrader.run_upgrade()
